<script language="SpeedScript">

define input parameter p_title as character.
define input parameter p_card_class as character.
define input parameter p_card_headerclass as character.

{alapdef.i}

</script>
	<div class="card `p_card_class`">
		<script language="speedscript">
		if p_title <> "" then do:
			</script>
			<div class="card-header `p_card_headerclass`">
				`p_title`
			</div>
			<script language="speedscript">
		end.
		</script>
		<div class="card-body">
<script language="speedscript">
