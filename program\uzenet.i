{chr_lista.i program_uzenet {1}}

function new_error returns character (input p_uzenet as character).
	if p_uzenet <> "" then do:
		uj_ke_program_uzenet("error",p_uzenet).
	end.
	gv_hiba = true.
	return "".
end function.

function new_notice returns character (input p_uzenet as character).
	if p_uzenet <> "" then do:
		uj_ke_program_uzenet("notice",p_uzenet).
	end.
	gv_notice = true.
	return "".
end function.

function new_warning returns character (input p_uzenet as character).
	if p_uzenet <> "" then do:
		uj_ke_program_uzenet("warning",p_uzenet).
	end.
	gv_warning = true.
	return "".
end function.
