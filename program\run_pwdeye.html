<script language="SpeedScript">

define input parameter p_position_X as character.
define input parameter p_position_Y as character.

{alapdef.i}

</script>
<style>
.pwdeyeclass
{
    position: absolute;
    top: `p_position_X`;
    right: `p_position_Y`;
    cursor: pointer;
    z-index: 10;
}
</style>
<script language="JavaScript">
function showpwd(p_pwdeye, p_password)
{
    //console.log("showpwd-" + p_password);
    $("#" + p_pwdeye + "_0").hide();
    $("#" + p_pwdeye + "_1").show();
    $("#" + p_password).attr('type', 'text');
}

function hidepwd(p_pwdeye, p_password)
{
    //console.log("hidepwd-" + p_password);
    $("#" + p_pwdeye + "_0").show();
    $("#" + p_pwdeye + "_1").hide();
    $("#" + p_password).attr('type', 'password');
}
</script>
<script language="SpeedScript">
