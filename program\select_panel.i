define {1} shared work-table select_panel
    field kod as character
    field nev as character
    field sorrend as character
    field igen as logical.

procedure reset_select_panel.
    for each select_panel:
        delete select_panel.
    end.
end procedure.

procedure uj_select_panel.
    define input parameter p_kod like select_panel.kod.
    define input parameter p_nev like select_panel.nev.
    define input parameter p_sorrend like select_panel.sorrend.
    define input parameter p_igen like select_panel.igen.
    create select_panel.
    assign
        select_panel.kod = p_kod
        select_panel.nev = p_nev
        select_panel.sorrend = p_sorrend
        select_panel.igen = p_igen
    no-error.
end procedure.