DEFINE INPUT PARAMETER IRELA AS INTEGER.
<PERSON><PERSON>NE OUTPUT PARAMETER LKIADE AS <PERSON><PERSON><PERSON>CAL INITIAL FALSE.
DEFINE VARIABLE CIKKSZAM AS CHARACTER.
DEFINE VARIABLE DFOGY AS DECIMAL.
DEFINE VARIABLE DKIZART AS DECIMAL.
 FIND FIRST MK.RFOGLTET WHERE MK.RFOGLTET.REL-AZON = IRELA NO-LOCK NO-ERROR.
  IF NOT AVAILABLE MK.RFOGLTET THEN
   DO:
    RETURN.
   END.
   CIKKSZAM = MK.RFOGLTET.Cik-Ksz.
   FIND FIRST MK.TKESZLET WHERE MK.TKESZLET.KOD = CIKKSZAM NO-LOCK.
   FOR EACH MK.RKESZLET WHERE MK.RKESZLET.KOD = TKESZLET.KOD NO-LOCK:
    FIND FIRST MK.<PERSON><PERSON><PERSON><PERSON>R WHERE MK.KRAKTAR.KOD = RKES<PERSON>LET.RAK-TKOD NO-LOCK NO-ERROR.
     IF AVAILABLE MK.KRAKTAR THEN
      DO:
       IF MK.<PERSON>RAKTAR.KIZ-AR THEN DKIZART = DKIZART + RKESZLET.MEN-NYI.
      END.
   END.
   DFOGY = MK.TKESZLET.MEN-NYI - DKIZART.
   FOR EACH MK.RFOGLTET where MK.RFOGLTET.Cik-Ksz = CIKKSZAM no-lock use-index CIKKPRIREL2_NDX:
    IF DFOGY < MK.RFOGLTET.Men-Nyi THEN
     DO:
      IF MK.RFOGLTET.REL-AZON2 = IRELA THEN LKIADE = FALSE.
     END. ELSE
     DO:
      IF MK.RFOGLTET.REL-AZON2 = IRELA THEN LKIADE = TRUE.
     END.    
     DFOGY = DFOGY - MK.RFOGLTET.Men-Nyi.
   END.
