procedure proc_xmlEncode.
	define input parameter p_text as longchar case-sensitive.
	define output parameter eredmeny as longchar case-sensitive.
	assign
		eredmeny = p_text
        eredmeny = replace(eredmeny, "&":U, "&amp~;":U)
		eredmeny = replace(eredmeny, "<":U, "&lt~;":U)
		eredmeny = replace(eredmeny, ">":U, "&gt~;":U)
        eredmeny = replace(eredmeny, "%":U, "&#37~;":U)
	no-error.
end procedure.

function xmlEncode returns longchar (input p_text as longchar).
	define variable eredmeny as longchar.
	run proc_xmlEncode (input p_text, output eredmeny).
	return eredmeny.
end function.

function xmlEncodeFile returns character (input p_inputfile as character, input p_outputfile as character).
    define variable filecontent as longchar.
    copy-lob from file p_inputfile to filecontent.
    filecontent = xmlEncode(filecontent).
    copy-lob filecontent to file p_outputfile.
	return "".
end function.
