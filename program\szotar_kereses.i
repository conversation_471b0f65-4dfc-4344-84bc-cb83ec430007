define variable par_szotar_kategoria as character.
define variable par_szotar_nyelv as character.
define variable par_szotar_kontextus as character.
define variable par_nem_leforditott as character.
define variable par_kod as character.
define variable par_szoveg as character.
define variable par_gyorsforditas as character.
define temp-table szotar_talalat like szotar.

procedure szotar_kereses_par.
	par_kod = get-field("par_kod").
	par_szoveg = get-field("par_szoveg").
	par_szotar_kategoria = get-field("par_szotar_kategoria").
	par_szotar_nyelv = get-field("par_szotar_nyelv").
	par_szotar_kontextus = get-field("par_szotar_kontextus").
	par_nem_leforditott = get-field("par_nem_leforditott").
	par_gyorsforditas = get-field("par_gyorsforditas").
end procedure.

procedure proc_szotar_kereses.
    define variable feltetel_kategoria as logical.
    define variable feltetel_nyelv as logical.
    define variable feltetel_kontextus as logical.
    define variable feltetel_nem_leforditott as logical.
    define variable feltetel_gyorsforditas as logical.
    define variable feltetel_mind as logical.
    assign
        feltetel_kategoria = false
        feltetel_nyelv = false
        feltetel_kontextus = false
        feltetel_nem_leforditott = false
        feltetel_gyorsforditas = true
        feltetel_mind = false
    no-error.
    if par_szotar_kategoria = "" or par_szotar_kategoria = szotar.kategoria then do:
        feltetel_kategoria = true.
    end.
    if par_szotar_nyelv = "" or par_szotar_nyelv = szotar.nyelv then do:
        feltetel_nyelv = true.
    end.
    if par_szotar_kontextus = "" then do:
        feltetel_kontextus = true.
    end.
    else do:
        find first szotar_kontextus where szotar_kontextus.kategoria = szotar.kategoria and szotar_kontextus.kod = szotar.kod and szotar_kontextus.kontextus = par_szotar_kontextus no-lock no-error.
        if available szotar_kontextus then do:
            feltetel_kontextus = true.
        end.
    end.
    if par_nem_leforditott = "" then do:
        feltetel_nem_leforditott = true.
    end.
    else do:
        find first buff-szotar where buff-szotar.kategoria = szotar.kategoria and buff-szotar.kod = szotar.kod and buff-szotar.nyelv = par_nem_leforditott no-lock no-error.
        if not available buff-szotar then do:
            feltetel_nem_leforditott = true.
        end.
    end.
    if par_gyorsforditas = "igen" then do:
        if szotar.kategoria begins "DB." then do:
            feltetel_gyorsforditas = false.
        end.
    end.
    if
    feltetel_kategoria = true and
    feltetel_nyelv = true and
    feltetel_kontextus = true and
    feltetel_nem_leforditott = true and
    feltetel_gyorsforditas = true then
    do:
        feltetel_mind = true.
    end.
    if feltetel_mind = true then do:
        create szotar_talalat.
        buffer-copy szotar to szotar_talalat.
    end.
end procedure.

procedure szotar_kereses.
	for each szotar_talalat:
		delete szotar_talalat.
	end.
	if par_kod <> "" then do:
	    for each szotar no-lock where szotar.kod begins par_kod:
            run proc_szotar_kereses.
        end.
	end.
	else do:
	    for each szotar no-lock:
            if index(szotar.szoveg,par_szoveg) > 0 or par_szoveg = "" then do:
                run proc_szotar_kereses.
            end.
        end.
	end.
end procedure.