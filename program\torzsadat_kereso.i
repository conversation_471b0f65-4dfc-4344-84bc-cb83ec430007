&if "{6}" <> "" &then
	&global-define torzsadakereso_procedure {6}
&else
	&global-define torzsadakereso_procedure {1}_{2}_2_{4}
&endif

define buffer buff-{&torzsadakereso_procedure} for {1}.

procedure proc_{&torzsadakereso_procedure}.
	define input parameter p_adat as {3}.
	define output parameter eredmeny as {5}.
	find first buff-{&torzsadakereso_procedure} where buff-{&torzsadakereso_procedure}.{2} = p_adat no-lock no-error.
	if available buff-{&torzsadakereso_procedure} then do:
		eredmeny = buff-{&torzsadakereso_procedure}.{4}.
	end.
end procedure.

function {&torzsadakereso_procedure} returns {5} (input p_adat as {3}).
	define variable eredmeny as {5}.
	run proc_{&torzsadakereso_procedure}
	(
		input p_adat,
		output eredmeny
	).
	return eredmeny.
end function.
