<script language="SpeedScript">

define input parameter p_selectname as character.
define input parameter p_cimke as character.
define input parameter p_settings as character.

define variable lv_balrajobbra as logical.
define variable lv_felle as logical.

{alapdef.i}

{basedef.i select_panel}

{chr_lista.i select_panel_param}

{basedef.i teszt}

lv_balrajobbra = true.
if letezik_select_panel_param("balrajobbra_kikapcsolva") then do:
    lv_balrajobbra = false.
end.
lv_felle = letezik_select_panel_param("felle").

run .\vcard_start.html (input p_cimke, input "", input "text-center").
</script>
<script language="JavaScript">
function `p_selectname`_ertek_epit()
{
    form_0.`p_selectname`.value = "";
    for (var i = 0; i < document.forms[0].select_`p_selectname`_igen.options.length; i++)
    {
        form_0.`p_selectname`.value = form_0.`p_selectname`.value + form_0.select_`p_selectname`_igen.options[i].value + "`tabchar`";
    }
}
</script>
<div class="row align-items-center text-center">
    <div class="col">
        <input type="hidden" name="`p_selectname`" value="">
        <select name="select_`p_selectname`_igen" id="select_`p_selectname`_igen" class="form-control" size="8">
            <script language="SpeedScript">
            for each select_panel no-lock where select_panel.igen = true by select_panel.sorrend:
                </script>
                <option value="`select_panel.kod`">`select_panel.nev`</option>
                <script language="SpeedScript">
            end.
            </script>
        </select>
    </div>
    <div class="col-md-1">
        <script language="SpeedScript">
        if lv_felle = true then do:
            </script>
            <div class="row">
                <span class="iconify mdi-menu-icon-size gomb_fel_le_balra_jobbra center" data-icon="mdi-arrow-up-box" onclick="fel(form_0.select_`p_selectname`_igen);"></span>
            </div>
            <script language="SpeedScript">
        end.
        if lv_balrajobbra = true then do:
            </script>
            <div class="row">
                <span class="iconify mdi-menu-icon-size gomb_fel_le_balra_jobbra center" data-icon="mdi-arrow-left-box" onclick="visz(form_0.select_`p_selectname`_nem,form_0.select_`p_selectname`_igen);"></span>
            </div>
            <div class="row">
                <span class="iconify mdi-menu-icon-size gomb_fel_le_balra_jobbra center" data-icon="mdi-arrow-right-box" onclick="visz(form_0.select_`p_selectname`_igen,form_0.select_`p_selectname`_nem);"></span>
            </div>
            <script language="SpeedScript">
        end.
        if lv_felle = true then do:
            </script>
            <div class="row">
                <span class="iconify mdi-menu-icon-size gomb_fel_le_balra_jobbra center" data-icon="mdi-arrow-down-box" onclick="le(form_0.select_`p_selectname`_igen);"></span>
            </div>
            <script language="SpeedScript">
        end.
        </script>
    </div>
    <div class="col">
        <select name="select_`p_selectname`_nem" id="select_`p_selectname`_nem" class="form-control" size="8">
            <script language="SpeedScript">
            for each select_panel no-lock where select_panel.igen = false by select_panel.sorrend:
                </script>
                <option value="`select_panel.kod`">`select_panel.nev`</option>
                <script language="SpeedScript">
            end.
            </script>
        </select>
    </div>
</div>
<script language="speedscript">
run .\vcard_stop.html.
</script>
