
{basedef.i debugfile}

function window_alert returns character (input p_alert as character).
	{&out} '<scri' 'pt language="JavaScript">~n'.
	{&out} 'window.alert("' p_alert '");~n'.
	{&out} '</scri' 'pt>~n'.
	return "".
end function.

function console_log returns character (input p_log as character).
	{&out} '<scri' 'pt language="JavaScript">~n'.
	{&out} 'console.log("' p_log '");~n'.
	{&out} '</scri' 'pt>~n'.
	return "".
end function.
