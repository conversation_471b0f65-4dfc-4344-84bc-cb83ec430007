TRIGGER PROCEDURE FOR WRITE OF EMILREKLTET.
IF MK.EMI<PERSON>EKLTET.OP = FALSE AND
   MK.EMILREKLTET.FI = FALSE AND
   MK.EMILREKLTET.PE = FALSE AND
   MK.EMILREKLTET.CI = FALSE THEN
    DO: 
    FIND FIRST MK.XPARAM WHERE MK.XPARAM.KOD = "MI" NO-LOCK NO-ERROR.
     IF AVAILABLE MK.XPARAM THEN
      DO:
       CASE XPARAM.ERT-EK_char:
        WHEN "O" THEN MK.EMILREKLTET.OP = TRUE.
        WHEN "F" THEN MK.EMILREKLTET.FI = TRUE.
        WHEN "P" THEN MK.EMILREKLTET.PE = TRUE.
        WHEN "C" THEN MK.EMILREKLTET.CI = TRUE.
       END.
      END.
     END. 

 IF MK.EMILREKLTET.SZA-LLCIMKOD = "" THEN
  DO:
    FIND FIRST XPARAM WHERE XPARAM.KOD = "cNeoParkod" NO-LOCK NO-ERROR.
     IF AVAILABLE XPARAM THEN
      DO:
       MK.EMILREKLTET.SZA-LLCIMKOD = XPARAM.ERT-EK_CHAR.  
      END.
  END. 
