/*define variable gv_csoport_kioszthat as character.*/
define variable lv_k as character.
define variable lv_i as integer.
define variable admin_dev as character.
define variable fejleszto_tag as logical.
define variable set_fejleszto_tag as logical.
define variable gv_user_kioszthat as character.
define buffer buff-tagsag for tagsag.
define buffer buff-csoport for csoport.

admin_dev = getparam("admin_dev","").

procedure proc_csoport_kioszthat.
	define input parameter p_kod as character.
	define output parameter eredmeny as character.
	eredmeny = p_kod.
	find first csoport where csoport.kod = p_kod no-lock no-error.
	if available csoport then do:
		eredmeny = csoport.kioszthat.
	end.
end procedure.

function csoport_kioszthat returns character (input p_kod as character).
	define variable eredmeny as character.
	run proc_csoport_kioszthat (input p_kod, output eredmeny).
	return eredmeny.
end function.

procedure proc_fejleszto_tag.
	define input parameter p_login as character.
	define output parameter p_csoport_tagsag as character.
	define output parameter p_fejleszto_tag as logical.
	for each buff-tagsag no-lock where buff-tagsag.login = p_login:
		p_csoport_tagsag = p_csoport_tagsag + buff-tagsag.csoport + tabchar.
		if lookup(buff-tagsag.csoport,admin_dev,linefeed) > 0 then do:
			p_fejleszto_tag = true.
		end.
	end.
	p_csoport_tagsag = trim(p_csoport_tagsag,tabchar).
	set_fejleszto_tag = true.
end procedure.

procedure user_kioszthat.
	define input parameter p_login as character.
	define output parameter p_user_kioszthat as character.
	define variable lv_csoport_tagsag as character.
	p_user_kioszthat = "".
	if set_fejleszto_tag = false then do:
		run proc_fejleszto_tag (input p_login, output lv_csoport_tagsag, output fejleszto_tag).
	end.
	if fejleszto_tag = true then do:
		for each buff-csoport no-lock:
			p_user_kioszthat = p_user_kioszthat + buff-csoport.kod + tabchar.
		end.
	end.
	else do:
		for each buff-tagsag no-lock where buff-tagsag.login = p_login:
			lv_k = csoport_kioszthat(buff-tagsag.csoport).
			do lv_i = 1 to num-entries(lv_k,tabchar):
				if entry(lv_i,lv_k,tabchar) <> "" then do:
					if lookup(entry(lv_i,lv_k,tabchar),p_user_kioszthat,tabchar) = 0 then do:
						p_user_kioszthat = p_user_kioszthat + entry(lv_i,lv_k,tabchar) + tabchar.
					end.
				end.
			end.
		end.
	end.
	p_user_kioszthat = trim(p_user_kioszthat,tabchar).
end procedure.

procedure proc_admin_kezelhet_user.
	define input parameter p_csoport_lista as character.
	define input parameter p_login_user as character.
	define output parameter eredmeny as logical.
	define variable hianyzo_csoport as character.
	eredmeny = true.
	for each buff-tagsag no-lock where buff-tagsag.login = p_login_user:
		if set_fejleszto_tag = false then do:
			if lookup(buff-tagsag.csoport,p_csoport_lista,tabchar) = 0 then do:
				eredmeny = false.
				if lookup(buff-tagsag.csoport,hianyzo_csoport) = 0 then do:
					hianyzo_csoport = hianyzo_csoport + "," + buff-tagsag.csoport.
				end.
			end.
		end.
	end.
	/*window_alert(hianyzo_csoport).*/
end procedure.

function admin_kezelhet_user returns logical(input p_csoport_lista as character, input p_login_user as character).
	define variable eredmeny as logical.
	run proc_admin_kezelhet_user (input p_csoport_lista, input p_login_user, output eredmeny).
	return eredmeny.
end function.

