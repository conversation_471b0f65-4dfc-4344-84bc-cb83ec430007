
{basedef.i env_var}

procedure task_info.
	define variable lv_WSEU as character.
	define variable lv_PROGRAM as character.
	define variable lv_i as integer.
	lv_WSEU = trim(web-context:exclusive-id,"WSEU=").
	lv_i = 1.
	repeat while program-name(lv_i) <> ?:
		if index(program-name(lv_i),"/") = 0 and index(program-name(lv_i),"\") = 0  then do:
			lv_PROGRAM = program-name(lv_i).
		end.
		lv_i = lv_i + 1.
	end.
	env_var("WS_TASK_INFO","").
	env_var("WS_WSEU",lv_WSEU).
	env_var("WS_PROGRAM",lv_PROGRAM).
	env_var("WS_LOGIN",user_login).
	env_var("WS_TASK_PROCESS","").
end procedure.

procedure task_info_STARTED.
	define input parameter p_task_info as character.
	run task_info.
	env_var("WS_TASK_INFO",p_task_info).
	env_var("WS_STATUS","STARTED").
	env_var("WS_STARTED",string(today) + " " + string(time,"hh:mm:ss")).
	env_var("WS_ENDED","").
end procedure.

procedure task_info_ENDED.
	run task_info.
	env_var("WS_TASK_INFO","").
	env_var("WS_STATUS","ENDED").
	env_var("WS_ENDED",string(today) + " " + string(time,"hh:mm:ss")).
end procedure.
