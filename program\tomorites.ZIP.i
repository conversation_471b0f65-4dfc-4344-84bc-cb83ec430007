define variable ch_ZIP as com-handle.
define work-table tomoritett_elemek
	field tomoritett_allomany as character
	field ZIP_file as character
	field utvonallal as logical
	field tomorites_szint as integer.

function reset_tomorites returns character.
	for each tomoritett_elemek:
		delete tomoritett_elemek.
	end.
end function.

procedure proc_uj_tomorites.
	define input parameter p_tomoritett_allomany as character.
	define input parameter p_ZIP_file as character.
	define input parameter p_utvonallal as logical.
	define input parameter p_tomorites_szint as integer.
	create tomoritett_elemek.
	assign
		tomoritett_elemek.tomoritett_allomany = p_tomoritett_allomany
		tomoritett_elemek.ZIP_file = p_ZIP_file
		tomoritett_elemek.utvonallal = p_utvonallal
		tomoritett_elemek.tomorites_szint = p_tomorites_szint
	no-error.
end procedure.

function uj_tomorites returns character (input p_tomoritett_allomany as character, input p_ZIP_file as character, input p_utvonallal as logical, input p_tomorites_szint as integer).
	run proc_uj_tomorites (input p_tomoritett_allomany, input p_ZIP_file, input p_utvonallal, input p_tomorites_szint).
	return "".
end function.

procedure proc_elvegez_tomorites.
	define variable lv_ZIP_EXE as character.
	define variable lv_commandline as character.
    lv_ZIP_EXE = "c:\7-Zip\7z.exe".
	for each tomoritett_elemek no-lock:
		file-info:file-name = tomoritett_elemek.tomoritett_allomany.
		if file-info:file-type <> ? then do:
			lv_commandline = lv_ZIP_EXE + " a " + tomoritett_elemek.ZIP_file + " " + tomoritett_elemek.tomoritett_allomany.
			os-command silent value(lv_commandline).
		end.
	end.
    reset_tomorites().
end procedure.

function elvegez_tomorites returns character.
	run proc_elvegez_tomorites.
	return "".
end function.
