&global-define fput put stream filestream unformatted
&global-define errput put stream errorstream unformatted

define variable num_errors as integer.
define variable crcfile as character format "X(64)".
define variable errorfile as character format "X(64)".
define variable CRC_hiba_cimzettek as character format "X(64)".
define {1} shared stream filestream.
define {1} shared stream errorstream.
define variable dbase as character format "X(16)".
define variable tabla as character format "X(16)".
define {1} shared work-table crc
	field crc_dbase like dbase
	field crc_tabla like tabla
	field ertek as integer
	field rendben as logical.

crcfile = param_appdir + "\crc\tablak.txt".
errorfile = tempdir + "\crc_error." + string(next-value(seq_tempfile)) + ".txt".
CRC_hiba_cimzettek = "<EMAIL>".
