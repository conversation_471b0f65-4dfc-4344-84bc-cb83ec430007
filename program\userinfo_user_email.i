{basedef.i userinfo_buffer}

procedure proc_user_email.
	define input parameter p_login as character.
	define output parameter eredmeny as character.
    find first userinfo_felhasznalo where userinfo_felhasznalo.login = p_login no-lock no-error.
    if available userinfo_felhasznalo then do:
		eredmeny = userinfo_felhasznalo.email.
	end.
    else do:
        find first userinfo_spc_partn where userinfo_spc_partn.login = p_login no-lock no-error.
		if available userinfo_spc_partn then do:
            eredmeny = userinfo_spc_partn.kap-email.
        end.
    end.
end procedure.

function user_email returns character (input p_login as character).
	define variable eredmeny as character.
	run proc_user_email (input p_login, output eredmeny).
	return eredmeny.
end function.