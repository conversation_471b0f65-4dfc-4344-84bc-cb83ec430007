<script language="SpeedScript">

define input parameter p_parent as character.
define input parameter p_tipus as character.
define input parameter p_title as character.

define variable lv_tipus as character.
define variable lv_uzenet as character.
define variable lv_growl_location as character.

{alapdef.i}

{uzenet.i}

{teszt.i}

case p_tipus:
	when "notice" then do:
		lv_tipus = "notice".
	end.
	when "warning" then do:
		lv_tipus = "warning".
	end.
	otherwise do:
		lv_tipus = "error".
	end.
end case.

lv_uzenet = "".

for each program_uzenet no-lock where program_uzenet.kod = lv_tipus:
	lv_uzenet = lv_uzenet + program_uzenet.ertek + tabchar.
end.
lv_uzenet = trim(lv_uzenet,tabchar).
lv_uzenet = replace(lv_uzenet,tabchar,"<br>").

/*reset_program_uzenet().*/
for each program_uzenet where program_uzenet.kod = lv_tipus:
	delete program_uzenet.
end.

/*lv_growl_location = "cc".*/
lv_growl_location = "br".
if growl_location <> "" then do:
	lv_growl_location = growl_location.
end.

if lv_uzenet <> "" then do:
	</script>
	<script language="JavaScript">
	`p_parent`$.growl.`lv_tipus`({ location: "`lv_growl_location`", size: "large", title: "`p_title`", message: "`lv_uzenet`",});
	</script>
	<script language="SpeedScript">
end.
