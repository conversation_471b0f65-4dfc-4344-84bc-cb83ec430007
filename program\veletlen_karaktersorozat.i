procedure proc_veletlen_karaktersorozat.
	define input parameter p_lista as character.
	define input parameter p_hossz as integer.
	define output parameter eredmeny as character.
	define variable lv_i as integer.
	define variable lv_karakter as integer.
	do lv_i = 1 to p_hossz:
		lv_karakter = random(1,length(p_lista)).
		eredmeny = eredmeny + substring(p_lista,lv_karakter,1).
	end.
end procedure.

function veletlen_karaktersorozat returns character (input p_lista as character, input p_hossz as integer).
	define variable eredmeny as character.
	run proc_veletlen_karaktersorozat (input p_lista, input p_hossz, output eredmeny).
	return eredmeny.
end function.
