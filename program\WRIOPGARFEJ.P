TRIGGER PROCEDURE FOR WRITE OF OPGARFEJ.

DEFINE VARIABLE ISAVNUM AS INTEGER.

RUN XSAVNMOS.P(OUTPUT ISAVNUM).

OPGARFEJ.SAV-NUM = ISAVNUM.

def var lvSTATUSZ as char.

IF LENGTH(OPGARFEJ.ALV-AZSZ) = 17 THEN
  DO:
   MK.OPGARFEJ.ALV-VEG = SUBSTRING(OPGARFEJ.ALV-AZSZ,10,8).
  END. ELSE
  DO:
   MK.OPGARFEJ.ALV-VEG = "".
  END.

  if OPGARFEJ.EUR-ARF = 0 then do:
    FIND FIRST MK.XPARAM WHERE MK.XPARAM.KOD = "D_PARAM_ARF" NO-LOCK NO-ERROR.
     IF AVAILABLE MK.XPARAM THEN
      DO:
       OPGARFEJ.EUR-ARF = MK.XPARAM.ERT-EK_DEC.
       OPGARFEJ.EUR-ARFDAT = string(today,"9999-99-99").
      END.
  end.

if M<PERSON>.OPGARFEJ.STA-TUSZ > "0"  then MK.OPGARFEJ.word = "#CLA".
if MK.OPGARFEJ.STA-TUSZ = "-3" or MK.OPGARFEJ.STA-TUSZ = "-4" or MK.OPGARFEJ.STA-TUSZ = "-5" then  MK.OPGARFEJ.word = "#PRA".
if MK.OPGARFEJ.STA-TUSZ = "-6" then MK.OPGARFEJ.word =  "#UCPRA".
if MK.OPGARFEJ.STA-TUSZ = "-10" then  MK.OPGARFEJ.word = "#UCCLA".

lvSTATUSZ = replace(MK.OPGARFEJ.STA-TUSZ,"-","M").

MK.OPGARFEJ.word = MK.OPGARFEJ.word + " #IMP" + substring(opgarfej.imp-kod,1,2) +  " #S" + lvSTATUSZ + " " + OPGARFEJ.ALV-AZSZ + " " + MK.OPGARFEJ.ALV-VEG + " #P" + opgarfej.par-kod + " " + left-trim(substring(opgarfej.hiv-szam,3),"0") + " " + opgarfej.hiv-szamkul.
MK.OPGARFEJ.word = MK.OPGARFEJ.word + " " + opgarfej.TIP-US + " " + HIB-AOK + " " + OPGARFEJ.HIV-SZAM.
FIND FIRST TMK.PARTNER WHERE TMK.PARTNER.KOD = MK.OPGARFEJ.PAR-KOD NO-LOCK NO-ERROR.
IF AVAILABLE TMK.PARTNER THEN MK.OPGARFEJ.word = MK.OPGARFEJ.word + " " + TMK.PARTNER.NEV.

MK.OPGARFEJ.veg-osszeg = MK.OPGARFEJ.alk-sum + MK.OPGARFEJ.mun-sum + MK.OPGARFEJ.ALV-KLTG + MK.OPGARFEJ.EGY-KLTG + MK.OPGARFEJ.shi-pcost + MK.OPGARFEJ.han-dlecost.