{basedef.i userinfo_buffer}

procedure proc_user_neve.
	define input parameter p_login as character.
	define output parameter eredmeny as character.
    find first userinfo_felhasznalo where userinfo_felhasznalo.login = p_login no-lock no-error.
    if available userinfo_felhasznalo then do:
		eredmeny = userinfo_felhasznalo.nev.
	end.
    else do:
        find first userinfo_spc_partn where userinfo_spc_partn.login = p_login no-lock no-error.
		if available userinfo_spc_partn then do:
            eredmeny = userinfo_spc_partn.szl-nev.
        end.
    end.
	if eredmeny = "" then do:
		eredmeny = p_login.
	end.
end procedure.

function user_neve returns character (input p_login as character).
	define variable eredmeny as character.
	run proc_user_neve (input p_login, output eredmeny).
	return eredmeny.
end function.

procedure proc_userid_2_nev.
	define input parameter p_id as integer.
	define output parameter eredmeny as character.
    find first userinfo_felhasznalo where userinfo_felhasznalo.kod = p_id no-lock no-error.
    if available userinfo_felhasznalo then do:
		eredmeny = userinfo_felhasznalo.nev.
	end.
	if eredmeny = "" then do:
		eredmeny = string(p_id).
	end.
end procedure.

function userid_2_nev returns character (input p_id as integer).
	define variable eredmeny as character.
	run proc_userid_2_nev (input p_id, output eredmeny).
	return eredmeny.
end function.