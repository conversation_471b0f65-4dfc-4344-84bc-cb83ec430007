define variable rar_exe as character.
define variable temp_RAR as character.
define variable temp_full_RAR as character.
define work-table tomoritett_elemek
	field tomoritett_allomany as character
	field RAR_file as character.
	
rar_exe = getparam("rar_exe","C:\Program Files\WinRAR\Rar.exe").

function reset_tomorites returns character.
	for each tomoritett_elemek:
		delete tomoritett_elemek.
	end.
end function.

procedure proc_uj_tomorites.
	define input parameter p_tomoritett_allomany as character.
	define input parameter p_RAR as character.
	create tomoritett_elemek.
	assign
		tomoritett_elemek.tomoritett_allomany = p_tomoritett_allomany
		tomoritett_elemek.RAR_file = p_RAR
	no-error.
end procedure.

function uj_tomorites returns character (input p_tomoritett_allomany as character, input p_RAR as character).
	run proc_uj_tomorites (input p_tomoritett_allomany, input p_RAR).
	return "".
end function.

procedure proc_elvegez_tomorites.
	define variable lv_commandline as character.
	for each tomoritett_elemek no-lock:
		file-info:file-name = tomoritett_elemek.tomoritett_allomany.
		if file-info:file-type <> ? then do:
			lv_commandline = '"' + rar_exe + '" a ' + tomoritett_elemek.RAR_file + " " + tomoritett_elemek.tomoritett_allomany.
			os-command silent value(lv_commandline).
		end.
	end.
	reset_tomorites().
end procedure.

function elvegez_tomorites returns character.
	run proc_elvegez_tomorites.
	return "".
end function.
