<script language="SpeedScript">

    define variable piac as character.
    define variable partner_kod as character.
    define variable allokacio as character.
    define variable cikklista as character.
    define variable ajax_begins as character.
    define variable cikkszam as character.
    define variable alvazszam as character.
    define variable muvelet_kod as character.
    define variable kod_mlap as character.
    define variable hivszam as character.
    define variable uzenet as character.
    define variable rows as character.
    define variable ipID as character.
    define variable qty as character.

    def temp-table ttsi field dat-e as character field qty as integer.
    define buffer bfcikk for mk.piecesh.
    define buffer brimpcalc for mk.rimpcalc.

    {alapdef.i new}

    {kodolas.i}

    {fordit.i}

    {init_login_state.i}

    {basedef.i joginfo}

    {hozzafer.i}

    {basedef.i teszt}

    {basedef.i json}

    {basedef.i utf8convert}

    {basedef.i cikkinfo}

    {chr_lista.i json_parameter new}

    {chr_lista.i json_warning new}

    {chr_lista.i json_error new}

    {basedef.i cikkinfo}

    {basedef.i formatum}

    procedure output-headers:
        run init_alapdef.
        output-content-type ("application/json; charset=iso-8859-2":u).
        program_base_name = "json".
    	program_name = program_base_name + ".html".
    	szotar_kategoria = "keretrendszer".
    	szotar_kontextus = program_name.
    	feladat = get-field("feladat").
        piac = get-field("piac").
        partner_kod = get-field("partner_kod").
        allokacio = get-field("allokacio").
        cikklista = get-field("cikklista").
        ajax_begins = get-field("ajax_begins").
        cikkszam = get-field("cikkszam").
        alvazszam = get-field("alvazszam").
        muvelet_kod = get-field("muvelet_kod").
        kod_mlap = get-field("kod_mlap").
        hivszam = get-field("hivszam").
        uzenet = get-field("message").
        rows = get-field("rows").
        ipID = get-field("id").
        qty = get-field("qty").
        term = get-field("term"). /* Autocomplete kereséshez kell, ebben küldi a javascript a begépelt karaktereket */
        uj_json_parameter("feladat", feladat).
        gv_formatum_decimal = "->,>>>,>>>,>>9.99".

    	case feladat:
            when "garkerv_garancia_adatok" then do:
                program_jog = "opel_garancialis_elszamolasok".
                uj_json_parameter("alvazszam", alvazszam).
            end.
            when "garkerv_oradij" then do:
                program_jog = "opel_garancialis_elszamolasok".
                uj_json_parameter("partner_kod", partner_kod).
                uj_json_parameter("allokacio", allokacio).
            end.
            when "garkerv_onchange_alvazszam" then do:
                program_jog = "opel_garancialis_elszamolasok".
                uj_json_parameter("alvazszam", alvazszam).
            end.
            when "garkerv_cikktorzs" then do:
                program_jog = "opel_garancialis_elszamolasok".
                uj_json_parameter("piac", piac).
                uj_json_parameter("partner_kod", partner_kod).
                uj_json_parameter("allokacio", allokacio).
                uj_json_parameter("ajax_begins", ajax_begins).
            end.
            when "garkerv_muvelettorzs" then do:
                program_jog = "opel_garancialis_elszamolasok".
                uj_json_parameter("piac", piac).
                uj_json_parameter("partner_kod", partner_kod).
                uj_json_parameter("allokacio", allokacio).
                uj_json_parameter("ajax_begins", ajax_begins).
            end.
            when "rim_sppqlist" then do:
                program_jog = "RIMSPPQ".
                uj_json_parameter("partner_kod", partner_kod).
            end.
            when "rim_item_confirm" then do:
                program_jog = "RIMSPPQ".
            end.
            when "rim_partdetails" then do:
                program_jog = "RIMSPPQ".
                uj_json_parameter("partner_kod", partner_kod).
                uj_json_parameter("cikkszam", cikkszam).
            end.
            when "rim_sum_rejected" then do:
                program_jog = "RIMSPPQ".
                uj_json_parameter("partner_kod", partner_kod).
            end.
            when "get_partname" then do:
                program_jog = "RIMSPPQ".
                uj_json_parameter("cikkszam", cikkszam).
            end.
            when "get_documents" then do:
                program_jog = "alk_reklamacio".
                uj_json_parameter("kod_mlap", kod_mlap).
            end.

        end case.
        run init_login_state.
    end.

    /* ************************************************************************************ */

    procedure garkerv_garancia_adatok.
        define variable lv_garkezd as character.
        define variable lv_extgarveg as character.
        find first mk.garkezd where mk.garkezd.alv = alvazszam no-lock no-error.
    	if avail mk.garkezd then do:
    		lv_garkezd = replace(mk.garkezd.gar-kezd,"-",".").
    		lv_extgarveg = replace(mk.garkezd.ext-garveg,"-",".").
    	end.
    	{&out} JSON_DATA_start.
        {&out}
        '"garkezd": "' + lv_garkezd + '",' +
        '"extgarveg": "' + lv_extgarveg + '"'.
        {&out} JSON_DATA_stop.
    end procedure.

    procedure garkerv_oradij.
        define variable oradij as decimal. /* a partnerhez + allocationhöz tartozó óradíj */
        RUN opgarmunar.p (input partner_kod, input allokacio, output oradij).
        {&out} JSON_DATA_start.
        {&out}
        '"oradij" : "' + string(oradij) + '"'.
        {&out} JSON_DATA_stop.
    end procedure.

    procedure garkerv_onchange_alvazszam.
        define variable lv_garkezd as character.
        define variable lv_extgarveg as character.
        define variable utolso_km like mk.opgarfej.kma-allas.
        find first mk.garkezd where mk.garkezd.alv = alvazszam no-lock no-error.
    	if avail mk.garkezd then do:
    		lv_garkezd = replace(mk.garkezd.gar-kezd,"-",".").
    		lv_extgarveg = replace(mk.garkezd.ext-garveg,"-",".").
    	end.
    	for each mk.opgarfej no-lock where mk.opgarfej.alv-azszam = alvazszam:
            if mk.opgarfej.kma-allas > utolso_km then utolso_km = mk.opgarfej.kma-allas.
    	end.
    	{&out} JSON_DATA_start.
        {&out}
        '"garkezd": "' + lv_garkezd + '",' +
        '"extgarveg": "' + lv_extgarveg + '",' +
        '"utolso_km": "' + string(utolso_km) + '"'.
        {&out} JSON_DATA_stop.
    end procedure.

    procedure garkerv_cikktorzs.
        DEFINE VARIABLE DEURSZORKER AS DECIMAL.
        DEFINE VARIABLE SZORZO AS DECIMAL.
        define variable elszamolasi_ar as decimal. /* elszámolási ár */
        define variable dealer_besz_ar as decimal. /* dealer besz ár */
        define variable dealer_kker_ar as decimal. /* dealer kisker ár */
        define variable wall_besz_ar as decimal. /* wallis besz ár */
        define variable kezelesi_koltseg as decimal. /* handling cost */
        define variable szallitasi_koltseg as decimal. /* shipping cost */
        define variable oradij as decimal. /* a partnerhez + allocationhöz tartozó óradíj */
        define variable lv_JSON_delimiter like JSON_delimiter.
        define variable elvalaszto as character.
        define variable elv_helyett as character.
        elvalaszto = ",".
        elv_helyett = " ".
        FIND FIRST MK.XPARAM WHERE MK.XPARAM.KOD = "opKERALKSZORZO" NO-LOCK NO-ERROR.
        IF AVAILABLE MK.XPARAM THEN DEURSZORKER = MK.XPARAM.ERT-EK_DEC.
        ELSE DEURSZORKER = 1.05.
        DEURSZORKER = 1.
        for each mk.piecesh where mk.piecesh.sza-llito = "op" and mk.piecesh.kod begins ajax_begins no-lock:
            if mk.piecesh.kod begins "OSP" then SZORZO = 1. else SZORZO = DEURSZORKER.
            RUN opgarpiecar_web.p(piac, mk.piecesh.kod, input allokacio, output elszamolasi_ar, output dealer_besz_ar, output dealer_kker_ar, output wall_besz_ar, output kezelesi_koltseg, output szallitasi_koltseg).
            {&out} lv_JSON_delimiter.
            {&out} JSON_DATA_start.
            {&out}
            '"value": "' + string(dealer_kker_ar * SZORZO) + '",' +
            '"label": "' + replace(trim(mk.piecesh.kod),elvalaszto,elv_helyett) + '",' +
            '"desc": "' + cikk_neve(mk.piecesh.kod, nyelv) + '",' +
            '"local": "' + (if mk.piecesh.loc-al then "1" else "0") + '",' +
            '"kezktg": "' + string(kezelesi_koltseg) + '",' +
            '"szallktg": "' + string(szallitasi_koltseg) + '",' +
            '"elszar": "' + string(elszamolasi_ar) + '"'.
            {&out} JSON_DATA_stop.
            lv_JSON_delimiter = ",".
        end.
    end procedure.

    procedure garkerv_muvelettorzs.
        define variable lv_JSON_delimiter like JSON_delimiter.
        for each MK.OPNORMFEJ no-lock where MK.OPNORMFEJ.kod begins ajax_begins:
            {&out} lv_JSON_delimiter.
            {&out} JSON_DATA_start.
            {&out}
            '"kod": "' + MK.OPNORMFEJ.kod  + '",' +
            '"nev": "' + MK.OPNORMFEJ.nev + '"'.
            {&out} JSON_DATA_stop.
            lv_JSON_delimiter = ",".
        end.
    end procedure.

    procedure rim_returnlist.
      define variable lv_JSON_delimiter like JSON_delimiter.
      define variable ar as decimal.
      define variable lvPAR as character.
      define variable lvSTAT as character.
      def var lvI as int.

      find first tmk.partner where tmk.partner.kod = partner_kod no-lock no-error.
      if not avail tmk.partner then next.
      for each rimreturnlist where rimreturnlist.par-kod = partner_kod and (rimreturnlist.qty-calc > 0 or rimreturnlist.qty-awd > 0)  no-lock:
        find first mk.piecesh where mk.piecesh.kod = rimreturnlist.cik-ksz no-lock no-error.
        if not avail mk.piecesh then next.
        lvSTAT = if rimreturnlist.hiv-szam = "" then fordit(szotar_kategoria,"nyitott",nyelv,"Nyitott",szotar_kontextus) else fordit(szotar_kategoria,"lezart",nyelv,"Lezárt",szotar_kontextus).

        /*lvI = lvI + 1.
        if lvI > 1 then next.*/

        {&out} lv_JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out}
        '"DT_RowId": "row_' + string(rowid(rimreturnlist)) + '",' +
        '"id": "' + string(rowid(rimreturnlist)) + '",' +
        '"parkod": "' + rimreturnlist.par-kod  + '",' +
        '"parnev": "' + tmk.partner.nev  + '",' +
        '"cikkszam": "' + rimreturnlist.cik-ksz  + '",' +
        '"ertek": "' + formatum_decimal(rimreturnlist.ert-ek)  + '",' +
        '"qty_calc": "' + formatum_integer(decimal(rimreturnlist.qty-calc))  + '",' +
        '"qty_dealer": "' + formatum_integer(decimal(rimreturnlist.qty-dealer))  + '",' +
        '"qty_awd": "' + formatum_integer(decimal(rimreturnlist.qty-awd))  + '",' +
        '"cre_dat": "' + rimreturnlist.cre-dat  + '",' +
        '"cre_tim": "' + rimreturnlist.cre-tim  + '",' +
        '"cre_all": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim  + '",' +
        '"tipus": "' + rimreturnlist.typ-e  + '",' +
        '"statusz": "' + lvSTAT + '",' +
        '"megnevezes": "' + piecesh.nev + '"'.
        {&out} JSON_DATA_stop.
        lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure rim_returnlist_all_dealers.
      define variable lv_JSON_delimiter like JSON_delimiter.
      define variable ar as decimal.
      define variable lvPAR as character.
      define variable lvSTAT as character.
      def var lvI as int.
      define variable param_year as character.
      define variable current_partner_kod as character.

      param_year = get-field("param_year").
      if param_year = "" then param_year = ?.

      /* Iterálunk a partnereken */
      for each tmk.partner no-lock:
        current_partner_kod = tmk.partner.kod.

        for each rimreturnlist where rimreturnlist.par-kod = current_partner_kod
          and (rimreturnlist.qty-calc > 0 or rimreturnlist.qty-awd > 0)
          and (param_year = ? or rimreturnlist.yea-r = param_year) no-lock:

          find first mk.piecesh where mk.piecesh.kod = rimreturnlist.cik-ksz no-lock no-error.
          if not avail mk.piecesh then next.
          lvSTAT = if rimreturnlist.hiv-szam = "" then fordit(szotar_kategoria,"nyitott",nyelv,"Nyitott",szotar_kontextus) else fordit(szotar_kategoria,"lezart",nyelv,"Lezárt",szotar_kontextus).

          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"DT_RowId": "row_' + string(rowid(rimreturnlist)) + '",' +
          '"id": "' + string(rowid(rimreturnlist)) + '",' +
          '"parkod": "' + rimreturnlist.par-kod  + '",' +
          '"parnev": "' + tmk.partner.nev  + '",' +
          '"cikkszam": "' + rimreturnlist.cik-ksz  + '",' +
          '"ertek": "' + formatum_decimal(rimreturnlist.ert-ek)  + '",' +
          '"qty_calc": "' + formatum_integer(decimal(rimreturnlist.qty-calc))  + '",' +
          '"qty_dealer": "' + formatum_integer(decimal(rimreturnlist.qty-dealer))  + '",' +
          '"qty_awd": "' + formatum_integer(decimal(rimreturnlist.qty-awd))  + '",' +
          '"cre_dat": "' + rimreturnlist.cre-dat  + '",' +
          '"cre_tim": "' + rimreturnlist.cre-tim  + '",' +
          '"cre_all": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim  + '",' +
          '"tipus": "' + rimreturnlist.typ-e  + '",' +
          '"statusz": "' + lvSTAT + '",' +
          '"megnevezes": "' + piecesh.nev + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
        end.
      end.
    end procedure.

    procedure rim_supportdb.
      define variable lv_JSON_delimiter like JSON_delimiter.
      define variable ar as decimal.
      define variable lvPAR as character.
      def var lvI as int.
      define variable param_year as character.

      param_year = get-field("param_year").
      if param_year = "" then param_year = ?.

      find first tmk.partner where tmk.partner.kod = partner_kod no-lock no-error.
      if not avail tmk.partner then next.
      for each rimsupport where rimsupport.par-kod = partner_kod
        and (param_year = ? or rimsupport.yea-r = param_year) no-lock:
        find first mk.piecesh where mk.piecesh.kod = rimsupport.cik-ksz no-lock no-error.
        if not avail mk.piecesh then next.

        /*lvI = lvI + 1.
        if lvI > 1 then next.*/

        {&out} lv_JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out}
        '"parkod": "' + rimsupport.par-kod + '",' +
        '"cikkszam": "' + rimsupport.cik-ksz + '",' +
        '"cikknev": "' + piecesh.nev + '",' +
        '"year": "' + rimsupport.yea-r + '",' +
        '"pys": "' + string(rimsupport.pys) + '",' +
        '"stock": "' + string(rimsupport.qty-stock) + '",' +
        '"dat_le": "' + rimsupport.dat-le + '",' +
        '"pyc": "' + string(rimsupport.pyc,"true/false") + '",' +
        '"dat_szla": "' + rimsupport.dat-szla + '",' +
        '"hiv_szla": "' + rimsupport.hiv-szla + '",' +
        '"dat_ps": "' + rimsupport.dat-ps + '",' +
        '"buy_ly": "' + string(rimsupport.qty-buyly) + '",' +
        '"sold_ly": "' + string(rimsupport.qty-soldly) + '",' +
        '"buy_pa": "' + string(rimsupport.qty-buyps) + '",' +
        '"sold_ps": "' + string(rimsupport.qty-soldps) + '",' +
        '"ps_buy_vs_sold": "' + (if rimsupport.qty-pslastapproval <> ? then string(rimsupport.qty-pslastapproval) else "") + '",' +
        '"protected_stock": "' + string(rimsupport.qty-pstotal) + '"'.
        {&out} JSON_DATA_stop.
        lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure rim_fullreturnlist.
      define variable lv_JSON_delimiter like JSON_delimiter.
      define variable ar as decimal.
      define variable lvPAR as character.
      def var lvI as int.
      def var lvDEV as char.
      def var lvQTYBIY as int.
      def var lvRETQTY as int.
      def var current_partner_kod as char.
      define variable param_year as character.

      param_year = get-field("param_year").
      if param_year = "" then param_year = ?.

      /* IterĂĄlunk a partnereken - ha van partner_kod, akkor csak azon egyen */
      for each tmk.partner where (partner_kod = "" or partner_kod = ? or tmk.partner.kod = partner_kod) no-lock:

        current_partner_kod = tmk.partner.kod.
        RUN dev(current_partner_kod, output lvDEV).

        for each rimreturnlist where rimreturnlist.par-kod = current_partner_kod
          and rimreturnlist.typ-e = "AutoCalc"
          and (param_year = ? or rimreturnlist.yea-r = param_year) no-lock:
          find first mk.piecesh where mk.piecesh.kod = rimreturnlist.cik-ksz no-lock no-error.
          if not avail mk.piecesh then next.

          lvRETQTY = if (rimreturnlist.qty-calc > 0) and not (rimreturnlist.qty-sold + rimreturnlist.qty-awdsold > 0) then rimreturnlist.qty-calc else 0.
          /*lvI = lvI + 1.
          if lvI > 1 then next.*/

          lvQTYBIY = rimreturnlist.qty-sold + rimreturnlist.qty-actual - rimreturnlist.qty-pyps.
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"year": "' + rimreturnlist.yea-r  + '",' +
          '"parkod": "' + rimreturnlist.par-kod  + '",' +
          '"cikkszam": "' + rimreturnlist.cik-ksz  + '",' +
          '"cikknev": "' + piecesh.nev + '",' +
          '"create": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim   + '",' +
          '"qty_pyps": "' + string(rimreturnlist.qty-pyps)   + '",' +
          '"qty_sold": "' + string(rimreturnlist.qty-sold)   + '",' +
          '"qty_actual": "' + string(rimreturnlist.qty-actual)   + '",' +
          '"qty_remaining": "' + string(rimreturnlist.qty-remaining)   + '",' +
          '"egysegar": "' + replace(string(rimreturnlist.egy-sar),".",",")  + '",' +
          '"devizanem": "' + lvDEV + '",' +
          '"ertek": "' + replace(string(rimreturnlist.ert-ek),".",",")  + '",' +
          '"limit_feletti": "' + string(rimreturnlist.lim-itok, "true/false")  + '",' +
          '"pkg_size": "' + string(rimreturnlist.pkg-size)  + '",' +
          '"pkg_full": "' + string(rimreturnlist.pkg-full, "true/false")  + '",' +
          '"qty_buyinyear": "' + string(lvQTYBIY)  + '",' +
          '"no_purchase": "' + string(lvQTYBIY <= 0, "true/false")  + '",' +
          '"qty_awdsold": "' + string(rimreturnlist.qty-awdsold)  + '",' + /* Új mező*/
          '"qty_awdsold_diff": "' + string(rimreturnlist.qty-sold - rimreturnlist.qty-awdsold)  + '",' + /* Új mező*/
          '"sell_out_sales": "' + string(rimreturnlist.qty-sold + rimreturnlist.qty-awdsold > 0, "true/false")  + '",' + /* Új mező*/
          '"qty_return": "' + string(lvRETQTY)  + '",' + /* Új mező*/
          '"qty_calc": "' + string(rimreturnlist.qty-calc)  + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
        end.
      end.
    end procedure.

    procedure rim_returnawupd.
      find first rimreturnlist where rowid(rimreturnlist) = to-rowid(ipID) and rimreturnlist.par-kod = partner_kod no-error.
      if avail rimreturnlist then do:
        if integer(qty) = rimreturnlist.qty-awd then do:
          uj_json_warning("param_error", fordit(szotar_kategoria,"nem_tortent_modositas",nyelv,"Nem történt módosítás",szotar_kontextus) + " !").
          return.
        end.
        rimreturnlist.qty-awd = integer(qty).
        rimreturnlist.cre-dat = string(today,"9999-99-99").
        rimreturnlist.cre-tim = string(time,"HH:MM:SS").
        rimreturnlist.typ-e = "AWUpdate".
        rimreturnlist.ert-ek = (rimreturnlist.ert-ek / rimreturnlist.qty-calc) * rimreturnlist.qty-awd.
        {&out} JSON_DATA_start.
        {&out}
        '"id": "' + string(rowid(rimreturnlist)) + '",' +
        '"ertek": "' + formatum_decimal(rimreturnlist.ert-ek)  + '",' +
        '"qty_awd": "' + formatum_integer(decimal(rimreturnlist.qty-awd))  + '",' +
        '"qty_dealer": "' + formatum_integer(decimal(rimreturnlist.qty-dealer))  + '",' +
        '"cre_dat": "' + rimreturnlist.cre-dat  + '",' +
        '"cre_tim": "' + rimreturnlist.cre-tim  + '",' +
        '"cre_all": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim  + '",' +
        '"tipus": "' + rimreturnlist.typ-e  + '"'.
        {&out} JSON_DATA_stop.
      end.
      else do:
        uj_json_error("param_error", fordit(szotar_kategoria,"a_modositas_sikertelen",nyelv,"A módosítás sikertelen",szotar_kontextus) + " !").
        return.
      end.
    end procedure.

    procedure rim_returndealerupd.
      find first rimreturnlist where rowid(rimreturnlist) = to-rowid(ipID) and rimreturnlist.par-kod = partner_kod no-error.
      if avail rimreturnlist then do:
        if integer(qty) > rimreturnlist.qty-calc then do:
          uj_json_error("param_error", fordit(szotar_kategoria,"a_kalkulalt_mennyisegnel_nem_adhato_meg_magasabb_ertek",nyelv,"A kalkulált mennyiségnél nem adható meg magasabb érték",szotar_kontextus) + " !").
          return.
        end.
        if integer(qty) = rimreturnlist.qty-dealer then do:
          uj_json_warning("param_error", fordit(szotar_kategoria,"nem_tortent_modositas",nyelv,"Nem történt módosítás",szotar_kontextus) + " !").
          return.
        end.
        rimreturnlist.qty-dealer = integer(qty).
        rimreturnlist.cre-dat = string(today,"9999-99-99").
        rimreturnlist.cre-tim = string(time,"HH:MM:SS").
        rimreturnlist.typ-e = "DealerUpdate".
        rimreturnlist.ert-ek = (rimreturnlist.ert-ek / (if rimreturnlist.qty-calc = 0 then 1 else rimreturnlist.qty-calc)) * rimreturnlist.qty-dealer.
        {&out} JSON_DATA_start.
        {&out}
        '"id": "' + string(rowid(rimreturnlist)) + '",' +
        '"ertek": "' + formatum_decimal(rimreturnlist.ert-ek)  + '",' +
        '"qty_awd": "' + formatum_integer(decimal(rimreturnlist.qty-awd))  + '",' +
        '"qty_dealer": "' + formatum_integer(decimal(rimreturnlist.qty-dealer))  + '",' +
        '"cre_dat": "' + rimreturnlist.cre-dat  + '",' +
        '"cre_tim": "' + rimreturnlist.cre-tim  + '",' +
        '"cre_all": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim  + '",' +
        '"tipus": "' + rimreturnlist.typ-e  + '"'.
        {&out} JSON_DATA_stop.
      end.
      else do:
        uj_json_error("param_error", fordit(szotar_kategoria,"a_modositas_sikertelen",nyelv,"A módosítás sikertelen",szotar_kontextus) + " !").
        return.
      end.
  end procedure.

  procedure rim_returnnew.
    define variable lvSTAT as character.
    if partner_kod = "" then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"hianyzik_a_partnerkod",nyelv,"Hiányzik a partnerkód",szotar_kontextus) + " !").
      return.
    end.
    if cikkszam = "" then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"cikkszam_kitoltese_kotelezo",nyelv,"Cikkszám kitöltése kötelező",szotar_kontextus) + " !").
      return.
    end.
    if integer(qty) = 0 then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"a_mennyiseg_nem_lehet_nulla",nyelv,"A mennyiég nem lehet nulla",szotar_kontextus) + " !").
      return.
    end.
    find first mk.piecesh where mk.piecesh.kod = cikkszam no-lock no-error.
    if not avail mk.piecesh then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"hibas_cikkszam",nyelv,"Hibás cikkszám",szotar_kontextus) + " !").
      return.
    end.
    find first rimreturnlist where rimreturnlist.par-kod = partner_kod and rimreturnlist.cik-ksz = cikkszam no-lock no-error.
    if avail rimreturnlist then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"a_cikkszam_mar_a_listaban_van",nyelv,"A cikkszám már a listában van",szotar_kontextus) + " !").
      return.
    end.
    find first tmk.partner where tmk.partner.kod = partner_kod no-lock no-error.
    if not avail tmk.partner then next.
    find first tmk.cegukart where tmk.cegukart.kod = partner_kod no-lock no-error.
    if not avail tmk.cegukart or tmk.cegukart.rim-start = "" then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"nem_rim_partner",nyelv,"Nem RIM partner!",szotar_kontextus) + " !").
      return.
    end.

    create rimreturnlist.
    rimreturnlist.par-kod = partner_kod.
    rimreturnlist.cik-ksz = cikkszam.
    rimreturnlist.qty-awd = integer(qty).
    rimreturnlist.cre-dat = string(today,"9999-99-99").
    rimreturnlist.cre-tim = string(time,"HH:MM:SS").
    rimreturnlist.typ-e = "AWCreate".
    rimreturnlist.ert-ek = 0 * rimreturnlist.qty-awd.
    lvSTAT = if rimreturnlist.hiv-szam = "" then fordit(szotar_kategoria,"nyitott",nyelv,"Nyitott",szotar_kontextus) else fordit(szotar_kategoria,"lezart",nyelv,"Lezárt",szotar_kontextus).
    {&out} JSON_DATA_start.
    {&out}
      '"DT_RowId": "row_' + string(rowid(rimreturnlist)) + '",' +
      '"id": "' + string(rowid(rimreturnlist)) + '",' +
      '"parkod": "' + rimreturnlist.par-kod  + '",' +
      '"parnev": "' + tmk.partner.nev  + '",' +
      '"cikkszam": "' + rimreturnlist.cik-ksz  + '",' +
      '"ertek": "' + formatum_decimal(rimreturnlist.ert-ek)  + '",' +
      '"qty_calc": "' + formatum_integer(decimal(rimreturnlist.qty-calc))  + '",' +
      '"qty_dealer": "' + formatum_integer(decimal(rimreturnlist.qty-dealer))  + '",' +
      '"qty_awd": "' + formatum_integer(decimal(rimreturnlist.qty-awd))  + '",' +
      '"cre_dat": "' + rimreturnlist.cre-dat  + '",' +
      '"cre_tim": "' + rimreturnlist.cre-tim  + '",' +
      '"cre_all": "' + rimreturnlist.cre-dat + " " + rimreturnlist.cre-tim  + '",' +
      '"tipus": "' + rimreturnlist.typ-e  + '",' +
      '"statusz": "' + lvSTAT + '",' +
      '"megnevezes": "' + mk.piecesh.nev + '"'.
    {&out} JSON_DATA_stop.
  end procedure.

  procedure rim_returnapprove.
    if partner_kod = "" then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"hianyzik_a_partnerkod",nyelv,"Hiányzik a partnerkód",szotar_kontextus) + " !").
      return.
    end.
    find first tmk.partner where tmk.partner.kod = partner_kod no-lock no-error.
    if not avail tmk.partner then next.
    find first tmk.cegukart where tmk.cegukart.kod = partner_kod no-lock no-error.
    if not avail tmk.cegukart or tmk.cegukart.rim-start = "" then do:
      uj_json_error("param_error", fordit(szotar_kategoria,"nem_rim_partner",nyelv,"Nem RIM partner!",szotar_kontextus) + " !").
      return.
    end.

    for each rimreturnlist where rimreturnlist.par-kod = partner_kod and rimreturnlist.hiv-szam = "":

    end.
    {&out} JSON_DATA_start.
    {&out} JSON_DATA_stop.
  end procedure.

    procedure rim_sppqlist.
        define variable lv_JSON_delimiter like JSON_delimiter.
        define variable ar as decimal.

        for each RIMPCALC where RIMPCALC.par-kod = partner_kod no-lock break by RIMPCALC.cik-ksz by RIMPCALC.dat-um:
            if last-of(RIMPCALC.cik-ksz) then do:
                find first mk.piecesh where mk.piecesh.kod = RIMPCALC.cik-ksz no-lock no-error.
                find first mk.rimcikkkat where mk.rimcikkkat.par-kod = RIMPCALC.par-kod and mk.rimcikkkat.cik-ksz = RIMPCALC.cik-ksz use-index parcikk_ndx no-lock no-error.
                run ar(RIMPCALC.par-kod, RIMPCALC.cik-ksz, output ar).
                {&out} lv_JSON_delimiter.
                {&out} JSON_DATA_start.
                {&out}
                '"id": "' + string(recid(RIMPCALC))  + '",' +
                '"part_number": "' + RIMPCALC.cik-ksz + '",' +
                '"description": "' + cikk_neve(RIMPCALC.cik-ksz, nyelv) + '",' +
                '"new_spq": "' + string(RIMPCALC.qty-calc) + '",' +
                '"previous_spq": "' + string(RIMPCALC.qty-prev) + '",' +
                '"price": "' + string(ar) + '",' +
                '"ns_code": "' + (if avail mk.rimcikkkat then mk.rimcikkkat.dms-rim-kat else "") + '",' +
                '"rf_code": "' + (if avail mk.rimcikkkat then mk.rimcikkkat.rim-kat else "") + '",' +
                '"l12mtd": "' + (if avail rimcikkkat then string(rimcikkkat.eve-s) else '0') + '",' +
                '"stock_on_hand": "' + string(RIMPCALC.sto-ck) + '",' +
                '"difference": "' + string(RIMPCALC.qty-calc - RIMPCALC.sto-ck) + '",' +
                '"created_date": "' + RIMPCALC.cre-dat + '",' +
                '"order_quantity": "' + (if avail mk.piecesh then string(mk.piecesh.cso-mag) else "") + '",' +
                '"status": "' + RIMPCALC.sta-tus + '",' +
                '"status_date": "' + RIMPCALC.sta-tdat + '"'.
                {&out} JSON_DATA_stop.
                lv_JSON_delimiter = ",".
            end.
        end.
    end procedure.

    procedure rim_item_confirm.
        define variable lv_JSON_delimiter like JSON_delimiter.
        define variable lvI as integer.
        define variable rimrecid as character.

        if rows = "" then do:
          uj_json_error("param_error", fordit(szotar_kategoria,"hianyzo_recid_parameter",nyelv,"Hiányzó RECID paraméter",szotar_kontextus) + " !").
          return.
        end.
        else do:
            do lvI = 1 to num-entries(rows):
                rimrecid = entry(lvI,rows).
                find first rimpcalc where recid(rimpcalc) = int(rimrecid) no-error.
                if not avail rimpcalc then do:
                  uj_json_error("param_error", fordit(szotar_kategoria,"hibas_recid_parameter",nyelv,"Hibás RECID paraméter",szotar_kontextus) + " !").
                  return.
                end.
                case muvelet_kod:
                  when "approve" then do:
                    if rimpcalc.sta-tus <> "da" then do:
                      rimpcalc.sta-tus = "da".
                      rimpcalc.sta-tdat = string(today,"9999-99-99").
                    end.
                  end.
                  when "reject" then do:
                    if rimpcalc.sta-tus <> "dr" then do:
                      rimpcalc.sta-tus = "dr".
                      rimpcalc.sta-tdat = string(today,"9999-99-99").
                    end.
                  end.
                end.
                find first rimpcalc where recid(rimpcalc) = int(rimrecid) no-lock no-error.
            end.
        end.
    end procedure.

    procedure rim_partdetails.
        define variable lv_JSON_delimiter like JSON_delimiter.
        define variable ar as decimal.
        define variable sales_info as character.
        def var lvDB as int.
        def var lvELOD as char.
        def var lvT as int.
        def var lvTS as char.
        def var lvON as char.
        def var lvOD as char.
    	def var lvQTYORDER as int.
    	def var glDAT as char.
    	def var lvCTMP as char.

        find first mk.piecesh where mk.piecesh.kod = cikkszam no-lock no-error.
        run ar(partner_kod, cikkszam, output ar).

        lvELOD = "".
        find first bfCIKK WHERE bfcikk.hel-ykod = "" and bfcikk.hel-ycikk = piecesh.kod no-lock no-error.
        if available bfCIKK THEN DO:
            lvELOD = bfCIKK.KOD.
        end. else do:
            DO lvT = 0 TO 9:
                lvTS = STRING(lvT , "9").
                find first bfCIKK WHERE bfcikk.hel-ykod = lvTS and bfcikk.hel-ycikk = piecesh.kod no-lock no-error.
                if available bfCIKK THEN DO:
                    lvELOD = lvELOD + ", " + bfCIKK.KOD.
                end.
            END.
        end.
        lvELOD = trim(lvELOD,",").

        for each rimcikkheti where rimcikkheti.par-kod = partner_kod and rimcikkheti.cik-ksz = cikkszam /*and rimcikkheti.weight <> 0*/ by rimcikkheti.ev by rimcikkheti.het:
            create ttsi.
            ttsi.dat-e = string(rimcikkheti.ev) + " #" + string(rimcikkheti.het,"99").
            ttsi.qty = rimcikkheti.qty-sold.
        end.

        lvDB = 1.
        for each ttsi by ttsi.dat-e desc:
          /*sales_info = sales_info + lv_JSON_delimiter + JSON_DATA_start +
          '"date": "' + ttsi.dat-e + ' week",' +
          '"qty": ' + string(ttsi.qty) + '' +
          JSON_DATA_stop.*/
          sales_info = sales_info +
          '"pnsid' + string(lvDB) + '": "' + ttsi.dat-e + ' week",' +
          '"pnsiq' + string(lvDB) + '": ' + string(ttsi.qty) + ','.
          if lvDB = 6 then leave.
          lvDB = lvDB + 1.
        end.
        sales_info = trim(sales_info,",").
        /*if sales_info <> "" then sales_info = '"sales_info":' + JSON_ARRAY_start + sales_info + JSON_ARRAY_stop.*/

        lvON = "N/A". lvOD = "N/A".
    	glDAT = string(today, "9999-99-99").
    	find first rimdailyfej where rimdailyfej.par-kod = partner_kod and rimdailyfej.dat-um = glDAT no-lock no-error.
    	if not avail rimdailyfej then glDAT = string(today - 1,"9999-99-99").
        /*find last rimdailytet where rimdailytet.par-kod = partner_kod and rimdailytet.cik-ksz = cikkszam use-index parcikdat_ndx no-lock no-error.*/
        find first RIMDAILYTET where RIMDAILYTET.PAR-KOD = partner_kod and RIMDAILYTET.CIK-KSZ = cikkszam and RIMDAILYTET.DAT-UM = glDAT no-lock no-error.
        if not avail rimdailytet then do:
          run hely(cikkszam, output lvCTMP).
          if lvCTMP <> "" then
          find first RIMDAILYTET where RIMDAILYTET.PAR-KOD = partner_kod and RIMDAILYTET.CIK-KSZ = lvCTMP and RIMDAILYTET.DAT-UM = glDAT no-lock no-error.
        end.
        /* 2024.05.27-én tettem bele, Imre kérésére. Ha nincs mai akkor az el?z? infókat jelenítsük meg a daily fájlból */
        if not avail rimdailytet then do:
          find last RIMDAILYTET where RIMDAILYTET.PAR-KOD = partner_kod and RIMDAILYTET.CIK-KSZ = cikkszam and RIMDAILYTET.DAT-UM < glDAT use-index parcikdat_ndx no-lock no-error.
          if not avail rimdailytet then do:
            run hely(cikkszam, output lvCTMP).
            if lvCTMP <> "" then
            find last RIMDAILYTET where RIMDAILYTET.PAR-KOD = partner_kod and RIMDAILYTET.CIK-KSZ = lvCTMP and RIMDAILYTET.DAT-UM < glDAT use-index parcikdat_ndx no-lock no-error.
          end.
        end.
        find last rimcikkkat where rimcikkkat.par-kod = partner_kod and rimcikkkat.cik-ksz = cikkszam use-index parcikk_ndx no-lock no-error.
        find last rimpcalc where rimpcalc.par-kod = partner_kod and rimpcalc.cik-ksz = cikkszam use-index parcikdat_ndx no-lock no-error.
        find last brimpcalc where brimpcalc.par-kod = partner_kod and brimpcalc.cik-ksz = cikkszam and recid(brimpcalc) <> recid(rimpcalc) use-index parcikdat_ndx no-lock no-error.
        for each rimorderlines where rimorderlines.par-kod = partner_kod and rimorderlines.cik-ksz = cikkszam no-lock by rimorderlines.par-kod by rimorderlines.cik-ksz by rimorderlines.kod-belso.
          find first rimorderhead where rimorderhead.kod-belso = rimorderlines.kod-belso no-lock no-error.
          if avail rimorderhead and rimorderhead.dat-um < string(today,"9999-99-99") and rimorderhead.sta-tusz < "90" then do:
            lvON = if rimorderhead.hiv-szam <> "" then rimorderhead.hiv-szam else rimorderhead.kod-belso.
            lvOD = rimorderhead.dat-um.
          end.
        end.
    	/*lvQTYORDER = if avail rimdailytet then rimdailytet.qty-order else 0.*/
        lvQTYORDER = 0.
        FOR EACH MK.VMEGRTET WHERE MK.VMEGRTET.PAR-KOD = partner_kod AND
          MK.VMEGRTET.CIK-KSZ = cikkszam AND
          (MK.VMEGRTET.NEO-STAT = "" OR
          MK.VMEGRTET.NEO-STAT = "1" OR
          MK.VMEGRTET.NEO-STAT = "4" OR
          MK.VMEGRTET.NEO-STAT = "6" OR
          MK.VMEGRTET.NEO-STAT = "7" OR
          MK.VMEGRTET.NEO-STAT = "8"):
            lvQTYORDER = lvQTYORDER + MK.VMEGRTET.MEN-NYI.
        END.


        {&out} JSON_DATA_start.
        {&out}
        '"part_number": "' + mk.piecesh.kod + '",' +
        '"part_description": "' + (if avail rimdailytet then cikk_neve(rimdailytet.cik-ksz, nyelv) else cikk_neve(piecesh.kod, nyelv)) + '",' +
        '"part_price": "' + replace(replace(replace(string(ar,"->,>>>,>>>,>>9.99")," ",""),","," "),".",".") + '",' +
        '"rim_parts": "' + (fordit(szotar_kategoria,(if avail rimpcalc then "igen" else "nem"),nyelv,(if avail rimpcalc then "Igen" else "Nem"),szotar_kontextus)) + '",' +
        '"superseeded_by": "' + MK.PIECESH.HEL-YCIKK + '",' +
        '"superseeds": "' + lvELOD + '",' +
        '"package_quantity": "' + string(mk.piecesh.cso-mag) + '",' +
        '"qty_stock": "' + (if avail rimdailytet then string(rimdailytet.qty-stock) else '0') + '",' +
        '"qty_order": "' + (string(lvQTYORDER)) + '",' +
        '"qty_backorder": "' + (if avail rimdailytet then string(rimdailytet.qty-backorder) else '0') + '",' +
        '"qty_prebooked": "' + (if avail rimdailytet then string(rimdailytet.qty-prebooked) else '0') + '",' +
        '"qty_lostsales": "' + (if avail rimdailytet then string(rimdailytet.qty-lostsales) else '0') + '",' +
        '"qty_locked": "' + '0' + '",' +
        '"qty_full_bin": "' + '0' + '",' +
        '"qty_per_job": "' + string(mk.piecesh.per-jobqty) + '",' +
        '"nls": "' + (fordit(szotar_kategoria,(if mk.piecesh.hel-ykod = "8" then "igen" else "nem"),nyelv,(if mk.piecesh.hel-ykod = "8" then "Igen" else "Nem"),szotar_kontextus)) + '",' +
        '"fixed_exclusion": "' + (fordit(szotar_kategoria,(if avail rimpcalc and rimpcalc.sta-tus = "dr" then "igen" else "nem"),nyelv,(if avail rimpcalc and rimpcalc.sta-tus = "dr" then "Igen" else "Nem"),szotar_kontextus)) + '",' +
        '"l12mtd": "' + (if avail rimcikkkat then string(rimcikkkat.eve-s) else '0') + '",' +
        '"l6mtd": "' + (if avail rimcikkkat then string(rimcikkkat.fel-eves) else '0') + '",' +
        '"daily_demand": "' + (if avail rimpcalc then string(RIMPCALC.dai-lysales) else '0') + '",' +
        '"frequency": "' + (if avail rimpcalc then string(RIMPCALC.fre-q) else '0') + '",' +
        '"cycle_stock": "' + '0' + '",' +
        '"lead_time_demand": "' + '0' + '",' +
        '"safety_days": "' + (if avail rimpcalc then string(RIMPCALC.gya-knap - 5) else '0') + '",' +
        '"safety_stock": "' + (if avail rimpcalc then string((RIMPCALC.gya-knap - 5) * RIMPCALC.dai-lysales) else '0') + '",' +
        '"extra_frequency_class": "' + '0' + '",' +
        '"last_order_date": "' + lvOD + '",' +
        '"control": "' + lvON + '",' +
        '"nsc": "' + (if avail rimcikkkat then string(rimcikkkat.dms-rim-kat) else '0') + '",' +
        '"nsp": "' + (if avail rimcikkkat then string(rimcikkkat.dms-rim-kat-prev) else '0') + '",' +
        '"rfc": "' + (if avail rimcikkkat then string(rimcikkkat.rim-kat) else '0') + '",' +
        '"rfp": "' + (if avail rimcikkkat then string(rimcikkkat.rim-kat-prev) else '0') + '",' +
        '"aswc": "' + (if avail rimpcalc then string(RIMPCALC.wee-klysales) else 'N/A') + '",' +
        '"aswp": "' + '0' + '",' +
        '"spqc": "' + (if avail rimpcalc then string(RIMPCALC.qty-calc) else 'N/A') + '",' +
        /*'"spqp": "' + (if avail rimpcalc then string(RIMPCALC.qty-prev) else '0') + '",' +*/
        '"spqp": "' + (if avail brimpcalc then string(BRIMPCALC.qty-calc) else 'N/A') + '",' +
        '"created_date": "' + (if avail rimpcalc then RIMPCALC.cre-dat else 'N/A') + '",' +
        '"approved_date": "' + (if avail rimpcalc then RIMPCALC.sta-tdat else 'N/A') + '",' +
        '"created_datep": "' + (if avail brimpcalc then BRIMPCALC.cre-dat else 'N/A') + '",' +
        '"approved_datep": "' + (if avail brimpcalc then BRIMPCALC.sta-tdat else 'N/A') + '"'.
     /*   '"approved_date": "' + (if avail rimpcalc and rimpcalc.sta-tus = "da" then RIMPCALC.sta-tdat else 'N/A') + '",' + */
        if sales_info <> "" then {&out} ',' + sales_info.
        {&out} JSON_DATA_stop.

    end procedure.

    procedure rim_sum_rejected.
        def var lvSUM as int.
        def var lvEXCLUDERATE as int init 5.
        def var lvSUMREJECTED as int.
        for each RIMPCALC where RIMPCALC.par-kod = partner_kod no-lock break by RIMPCALC.cik-ksz by RIMPCALC.dat-um:
            if last-of(RIMPCALC.cik-ksz) then do:
                if RIMPCALC.qty-calc = 0 and RIMPCALC.qty-prev = 0 and RIMPCALC.sto-ck = 0 then next.
                if RIMPCALC.qty-calc = 0 and RIMPCALC.qty-prev = 0 then next.
                lvSUM = lvSUM + 1.
                if rimpcalc.sta-tus = "dr" then lvSUMREJECTED = lvSUMREJECTED + 1.
            end.
        end.
        {&out} JSON_DATA_start.
        {&out}
        '"parts_quantity": ' + string(lvSUM) + ',' +
        '"parts_max_reject": ' + string(round(lvSUM * lvEXCLUDERATE / 100,0)) + ',' +
        '"parts_can_be_reject": ' + string(round(lvSUM * lvEXCLUDERATE / 100,0) - lvSUMREJECTED) + ',' +
        '"parts_rejected": ' + string(lvSUMREJECTED).
        {&out} JSON_DATA_stop.
    end procedure.

    procedure invoice_for_claim.
      define variable lv_JSON_delimiter like JSON_delimiter.
      def var lvDAYS as int init 10.
      find first cegukart where cegukart.kod = partner_kod no-lock no-error.
      if avail cegukart then do:
        IF TMK.CEGUKART.BRA <> "" THEN DO:
          FIND FIRST MK.NEORAKT WHERE MK.NEORAKT.KOD = TMK.CEGUKART.BRA NO-LOCK NO-ERROR.
          IF AVAILABLE MK.NEORAKT THEN DO:
            if NEORAKT.REK-LNAPHAT <> 0 then lvDAYS = NEORAKT.REK-LNAPHAT.
          END.
        END.
      end.
      for each atmszlafej where atmszlafej.par-kod = partner_kod
        and atmszlafej.kel-dat >= string(today - lvDAYS, "9999-99-99")
        no-lock by atmszlafej.hiv-szam:
        if (substr(atmszlafej.hiv-szam, length(atmszlafej.hiv-szam) - length(hivszam) + 1)) <> hivszam then next.
        {&out} lv_JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out}
        '"invoice_number": "' + atmszlafej.hiv-szam + '",' +
        '"invoice_date": "' + atmszlafej.kel-dat + '"'.
        {&out} JSON_DATA_stop.
        lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure invoicelines_for_claim.
      define variable lv_JSON_delimiter like JSON_delimiter.
      def var lvDAYS as int init 10.
      for each atmszlatet where atmszlatet.hiv-szam = hivszam
        no-lock by atmszlatet.cik-kszam:
        for each vmegrdoboz where vmegrdoboz.rel-azon2 = atmszlatet.rel-azon2 no-lock:
          find first emilrekltet where emilrekltet.tet-rel = MK.VMEGRDOBOZ.REL-AZON2 no-lock no-error.
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"relazon": ' + string(vmegrdoboz.rel-azon) + ',' +
          '"box_number": "' + vmegrdoboz.cso-mkod + '",' +
          '"item_number": "' + atmszlatet.cik-kszam + '",' +
          '"item_qty": ' + string(vmegrdoboz.men-nyi) + ',' +
          '"claim_num": "' + (if avail emilrekltet then emilrekltet.cla-imnum else "") + '",' +
          '"claim_dat": "' + (if avail emilrekltet then emilrekltet.cla-imdat else "") + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
        end.
      end.
    end procedure.

    procedure get_partname.
        find first mk.piecesh where mk.piecesh.kod = cikkszam no-lock no-error.
        {&out} JSON_DATA_start.
        {&out}
        '"name" : "' + (if avail mk.piecesh then mk.piecesh.nev else "N/A") + '"'.
        {&out} JSON_DATA_stop.
    end procedure.

    procedure get_documents.
      define variable lv_JSON_delimiter like JSON_delimiter.
      for each docfej where docfej.kod-mlap = kod_mlap no-lock:
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"relazon": ' + string(docfej.rel-azon) + ',' +
          '"filenev": "' + docfej.fil-enev + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure del_document.
      find first docfej where docfej.rel-azon = int(hivszam) share-lock no-error.
      if avail docfej then do:
        docfej.kod-mlap = "".
      end.
      /*run get_documents.*/
    end procedure.

    procedure store_message.
      define variable lvT as integer initial 0.
      find first MK.VMEGRDOBOZ where MK.VMEGRDOBOZ.REK-LSZAM = hivszam and vmegrdoboz.par-kod = partner_kod no-lock no-error.
      if avail mk.vmegrdoboz then do:
          CREATE MK.HBEJUZENET.
          lvT = NEXT-VALUE(RELAZON,MK).
          MK.HBEJUZENET.HIV-SZAM = hivszam.
          MK.HBEJUZENET.IRA-NY    = "B".
          MK.HBEJUZENET.REL-AZON  = mk.vmegrdoboz.rel-azon.
          MK.HBEJUZENET.FLT-ENGSTAT = "".
          MK.HBEJUZENET.MEG-J = utf82iso(uzenet).
          MK.HBEJUZENET.MEG-JTIP  = "KM".
          /*MK.MLAPFLTLOG.REL-AZON2 = lvT.*/
          MK.HBEJUZENET.KEL-DAT   = STRING(TODAY,{DFORMAT.I}).
          MK.HBEJUZENET.KEL-TIM   = STRING(TIME,{TFORMAT.I}).
          /*MK.MLAPFLTLOG.KEL-USR   = "WEB".*/
          MK.HBEJUZENET.KEL-USR = piac.
          MK.HBEJUZENET.ESE-MENY = "Üzenet küldése import?rnek".
          RELEASE MK.HBEJUZENET.
          /*output to e:\opel\template\!uzenet.txt.
          message utf82iso(uzenet).
          output close.*/
      end.
    end procedure.

    procedure get_messages.
      define variable lv_JSON_delimiter like JSON_delimiter.
      FOR EACH MK.HBEJUZENET WHERE MK.HBEJUZENET.HIV-SZAM = hivszam NO-LOCK by MK.HBEJUZENET.KEL-DAT by MK.HBEJUZENET.KEL-TIM :
            {&out} lv_JSON_delimiter.
            {&out} JSON_DATA_start.
            {&out}
            '"relazon": "' + string(hbejuzenet.rel-azon) + '",' +
            '"hivszam": "' + hbejuzenet.hiv-szam + '",' +
            '"irany": "' + hbejuzenet.ira-ny + '",' +
            '"uzenet": "' + replace(replace(replace(hbejuzenet.meg-j,'"',"'"),chr(10),""),chr(13)," ") + '",' +
            '"esemeny": "' + hbejuzenet.ese-meny + '",' +
            '"felhasznalo": "' + hbejuzenet.kel-usr + '",' +
            '"datum": "' + hbejuzenet.kel-dat + " " + hbejuzenet.kel-tim + '"'.
            {&out} JSON_DATA_stop.
            lv_JSON_delimiter = ",".
      END.
    end procedure.


    define temp-table boxes
    fields rel-azon as int
    fields cso-mkod as char
    fields men-nyi as dec
    fields cla-imnum as char
    fields cla-imdat as char.

    procedure boxes_for_claim.
      define variable lv_JSON_delimiter like JSON_delimiter.
      def var lvDAYS as int init 10.
      for each atmszlatet where atmszlatet.hiv-szam = hivszam
        no-lock by atmszlatet.cik-kszam:
        for each vmegrdoboz where vmegrdoboz.rel-azon2 = atmszlatet.rel-azon2 no-lock:
          find first emilrekltet where emilrekltet.tet-rel = MK.VMEGRDOBOZ.REL-AZON2 no-lock no-error.
          find first boxes where boxes.cso-mkod = vmegrdoboz.cso-mkod no-error.
          if not avail boxes then do:
            create boxes.
            boxes.rel-azon = vmegrdoboz.rel-azon.
            boxes.cso-mkod = vmegrdoboz.cso-mkod.
          end.
          boxes.men-nyi = boxes.men-nyi + vmegrdoboz.men-nyi.
          boxes.cla-imnum = (if avail emilrekltet then emilrekltet.cla-imnum else "").
          boxes.cla-imdat = (if avail emilrekltet then emilrekltet.cla-imdat else "").
        end.
      end.
      for each boxes by boxes.cso-mkod:
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"relazon": "' + boxes.cso-mkod /*string(boxes.rel-azon)*/ + '",' +
          '"box_number": "' + boxes.cso-mkod + '",' +
          '"item_number": "",' +
          '"item_qty": ' + string(boxes.men-nyi) + ',' +
          '"claim_num": "' + boxes.cla-imnum + '",' +
          '"claim_dat": "' + boxes.cla-imdat + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure discrepancy.
      define variable lv_JSON_delimiter like JSON_delimiter.
      for each trekl no-lock by trekl.kod:
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"kod": "' + trekl.kod + '",' +
          '"nev": "' + trekl.nev + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
      end.
    end.

    procedure piecesh.
      define variable lv_JSON_delimiter like JSON_delimiter.
      for each mk.piecesh where /*mk.piecesh.sza-llito = "SY" and*/ mk.piecesh.kod begins ajax_begins no-lock:
          {&out} lv_JSON_delimiter.
          {&out} JSON_DATA_start.
          {&out}
          '"label": "' + mk.piecesh.kod + '",' +
          '"desc": "' + mk.piecesh.nev + '"'.
          {&out} JSON_DATA_stop.
          lv_JSON_delimiter = ",".
      end.
    end procedure.

    procedure ar.
      def input param ipPAR as char.
      def input param ipCSZ as char.
      def output param opAR as dec.
      FIND FIRST TMK.CEGUKART WHERE CEGUKART.KOD = ipPAR NO-LOCK NO-ERROR.
      FIND FIRST MK.PIECAR WHERE MK.PIECAR.KOD = ipCSZ NO-LOCK NO-ERROR.
      IF AVAILABLE MK.PIECAR AND AVAILABLE TMK.CEGUKART THEN DO:
        IF TMK.CEGUKART.ORS-ZKOD = "HU" THEN DO:
          opAR = MK.PIECAR.DEA-LARHU.
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "SI" THEN DO:
          opAR = MK.PIECAR.DEA-LARSI.
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "HR" THEN DO:
          opAR = MK.PIECAR.DEA-LARHR.
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "BA" THEN DO:
          opAR = MK.PIECAR.DEA-LARBA.
        END.
      END.
    end procedure.

    procedure dev.
      def input param ipPAR as char.
      def output param opDEV as char.
      FIND FIRST TMK.CEGUKART WHERE CEGUKART.KOD = ipPAR NO-LOCK NO-ERROR.
      IF AVAILABLE TMK.CEGUKART THEN DO:
        IF TMK.CEGUKART.ORS-ZKOD = "HU" THEN DO:
          opDEV = "HUF".
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "SI" THEN DO:
          opDEV = "EUR".
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "HR" THEN DO:
          opDEV = "EUR".
        END.
        IF TMK.CEGUKART.ORS-ZKOD = "BA" THEN DO:
          opDEV = "EUR".
        END.
      END.
    end procedure.



    procedure hely:
      def input param ipCSZ as char.
      def output param opC as char.

      def var lvHC as char.

      find first bfcikk where bfcikk.kod = ipCSZ no-lock no-error.
      if not avail bfcikk or (avail bfcikk and bfcikk.hel-ykod = "") then do:
        opC = ipCSZ.
        return.
      end.
      lvHC = bfcikk.hel-ycikk.
      for each bfcikk where bfcikk.kod = lvHC no-lock:
        run hely(bfcikk.kod, output opC).
      end.
    end procedure.


    /* ************************************************************************************ */

    {&out} JSON_DATA_start.

    if program_jog <> "" then do:
        bejelentkezve = kod_ok(kodolt_login) and login_hozzafer(user_login,program_jog).
    end.
    else do:
        bejelentkezve = kod_ok(kodolt_login).
    end.
    /*bejelentkezve = true.*/

    {&out} '"parameter" : '.
    {&out} JSON_ARRAY_start.
    JSON_delimiter = "".
    for each json_parameter no-lock:
        {&out} JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out} '"code" : "' json_parameter.kod '",'.
        {&out} '"text" : "' json_parameter.ertek '"'.
        {&out} JSON_DATA_stop.
        JSON_delimiter = ",".
    end.
    {&out} JSON_ARRAY_stop.

    JSON_delimiter = ",".
    {&out} JSON_delimiter.

    {&out} '"data" : '.
    {&out} JSON_ARRAY_start.

    if bejelentkezve then do:
        case feladat:
            when "garkerv_garancia_adatok" then do:
                run garkerv_garancia_adatok.
            end.
            when "garkerv_oradij" then do:
                run garkerv_oradij.
            end.
            when "garkerv_onchange_alvazszam" then do:
                run garkerv_onchange_alvazszam.
            end.
            when "garkerv_cikktorzs" then do:
                run garkerv_cikktorzs.
            end.
            when "garkerv_muvelettorzs" then do:
                run garkerv_muvelettorzs.
            end.
            when "rim_sppqlist" then do:
                run rim_sppqlist.
            end.
            when "rim_returnlist" then do:
                run rim_returnlist.
            end.
            when "rim_returnlist_all_dealers" then do:
                run rim_returnlist_all_dealers.
            end.
            when "rim_fullreturnlist" then do:
                run rim_fullreturnlist.
            end.
            when "rim_supportdb" then do:
                run rim_supportdb.
            end.
            when "rim_returnawupd" then do:
                run rim_returnawupd.
            end.
            when "rim_returndealerupd" then do:
                run rim_returndealerupd.
            end.
            when "rim_returnnew" then do:
                run rim_returnnew.
            end.
            when "rim_returnapprove" then do:
                run rim_returnapprove.
            end.
            when "rim_item_confirm" then do:
                run rim_item_confirm.
            end.
            when "rim_partdetails" then do:
                run rim_partdetails.
            end.
            when "rim_sum_rejected" then do:
                run rim_sum_rejected.
            end.
            when "invoice_for_claim" then do:
                run invoice_for_claim.
            end.
            when "invoicelines_for_claim" then do:
                run invoicelines_for_claim.
            end.
            when "boxes_for_claim" then do:
                run boxes_for_claim.
            end.
            when "discrepancy" then do:
                run discrepancy.
            end.
            when "piecesh" then do:
                run piecesh.
            end.
            when "get_partname" then do:
                run get_partname.
            end.
            when "get_documents" then do:
                run get_documents.
            end.
            when "del_document" then do:
                run del_document.
            end.
              when "store_message" then do:
                  run store_message.
              end.
              when "get_messages" then do:
                  run get_messages.
              end.
        end case.
    end.
    else do:
        uj_json_error("AUTH_ERROR", fordit(szotar_kategoria,"hozzaferes_megtagadva",nyelv,"Hozzáférés megtagadva",szotar_kontextus) + " !").
        /*uj_json_warning("feladat", feladat).*/
    end.
    {&out} JSON_ARRAY_stop.

    JSON_delimiter = ",".
    {&out} JSON_delimiter.

    {&out} '"error" : '.
    {&out} JSON_ARRAY_start.
    JSON_delimiter = "".
    for each json_error no-lock:
        {&out} JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out} '"code" : "' json_error.kod '",'.
        {&out} '"text" : "' json_error.ertek '"'.
        {&out} JSON_DATA_stop.
        JSON_delimiter = ",".
    end.
    {&out} JSON_ARRAY_stop.

    JSON_delimiter = ",".
    {&out} JSON_delimiter.

    {&out} '"warning" : '.
    {&out} JSON_ARRAY_start.
    JSON_delimiter = "".
    for each json_warning no-lock:
        {&out} JSON_delimiter.
        {&out} JSON_DATA_start.
        {&out} '"code" : "' json_warning.kod '",'.
        {&out} '"text" : "' json_warning.ertek '"'.
        {&out} JSON_DATA_stop.
        JSON_delimiter = ",".
    end.
    {&out} JSON_ARRAY_stop.

    {&out} JSON_DATA_stop.
</script>
