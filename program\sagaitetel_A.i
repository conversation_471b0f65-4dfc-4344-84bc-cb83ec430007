if trim(mk.opgarvii.refpr{1}) <> "" then do:
    create sagaitetel.
    assign
        sagaitetel.tipus = "A"
        sagaitetel.sorrend = "{1}"
        sagaitetel.kod = trim(mk.opgarvii.refpr{1})
        sagaitetel.donteskod = mk.opgarvii.decapr{1}
        sagaitetel.uzenetkod = mk.opgarvii.mesapr{1}
        sagaitetel.kezelesi_koltseg = mk.opgarvii.mtfapr{1}
        sagaitetel.ar = mk.opgarvii.mtupr{1}
        sagaitetel.mennyiseg = mk.opgarvii.qtepr{1}
    no-error.
end.
