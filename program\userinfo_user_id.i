{basedef.i userinfo_buffer}

procedure proc_user_id.
	define input parameter p_login as character.
	define output parameter eredmeny as integer.
    find first userinfo_felhasznalo where userinfo_felhasznalo.login = p_login no-lock no-error.
    if available userinfo_felhasznalo then do:
		eredmeny = userinfo_felhasznalo.kod.
	end.
    /*else do:
        find first userinfo_spc_partn where userinfo_spc_partn.login = p_login no-lock no-error.
		if available userinfo_spc_partn then do:
            eredmeny = userinfo_spc_partn.szl-nev.
        end.
    end.
	if eredmeny = "" then do:
		eredmeny = p_login.
	end.*/
end procedure.

function user_id returns integer (input p_login as character).
	define variable eredmeny as integer.
	run proc_user_id (input p_login, output eredmeny).
	return eredmeny.
end function.