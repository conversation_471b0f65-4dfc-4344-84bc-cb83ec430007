{basedef.i userinfo_buffer}

{basedef.i partnerinfo_partner_markak}

procedure proc_user_markak.
	define input parameter p_login as character.
	define output parameter eredmeny as character.
    find first userinfo_felhasznalo where userinfo_felhasznalo.login = p_login no-lock no-error.
    if available userinfo_felhasznalo then do:
		eredmeny = userinfo_felhasznalo.markak.
		if eredmeny = "" then do:
			eredmeny = partner_markak(userinfo_felhasznalo.partner).
		end.
	end.
    else do:
        find first userinfo_spc_partn where userinfo_spc_partn.login = p_login no-lock no-error.
		if available userinfo_spc_partn then do:
            eredmeny = userinfo_spc_partn.markak.
        end.
    end.
end procedure.

function user_markak returns character (input p_login as character).
	define variable eredmeny as character.
	run proc_user_markak (input p_login, output eredmeny).
	return eredmeny.
end function.