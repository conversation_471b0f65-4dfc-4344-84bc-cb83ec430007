<script language="SpeedScript">

  define variable table_id as character.
  define variable param_partnerkod as character.
  define variable rim_admin as logical initial false.
  define variable partner_adm as character.
  define variable param_year as character.
  define variable param_kereses as character.

  define new shared work-table work-rimreturnlist like rimreturnlist.
  define buffer buf-rimreturnlist for rimreturnlist.
  define work-table ttyears
  fields yea-r as char.
  
  {alapdef.i new}

  {felt_disp.i}

  {menu.i new}

  {teszt.i}

  {kodolas.i}

  {fordit.i}

  {init_login_state.i}

  {output-headers.i}

  {basedef.i joginfo}

  {hozzafer.i}

  {chr_lista.i layout new}

  {basedef.i uzenet new}

  {basedef.i userinfo}

  {basedef.i partnerinfo}

  {basedef.i mezo_date}

  {basedef.i datetime}

  {basedef.i formatum}


  procedure local_output_headers.
    table_id = get-field("table_id").
    program_base_name = "rim_returnlist".
    program_name = program_base_name + ".html".
    program_jog = "RIMRETURNLIST".
    szotar_kategoria = "rim".
    szotar_kontextus = program_name.
    param_kereses = get-field("param_kereses").
    param_partnerkod = partner_tmkkod(user_partn).
    find first spc_partn where spc_partn.login = user_login no-lock no-error.
    if available spc_partn then do:
        param_partnerkod = spc_partn.tmk-kod.
    end.
    partner_adm = get-field("partner_adm").
    if partner_adm <> "" then param_partnerkod = partner_adm.
    param_year = get-field("param_year").
  end procedure.

  procedure szurofeltetel_mezok.
	</script>
    <input type="hidden" name="param_kereses" value="`param_kereses`">
    <script language="SpeedScript">
  end procedure.

  procedure lista.
    run szurofeltetel_mezok.
  </script>
  <script language="javascript">
    function formatToHungarian(value) {
        if (!value) return value;
        
        var str = value.toString();
        
        // Eltávolítjuk az ezres elválasztókat
        str = str.replace(/,/g, '');
        
        // Ha van tizedes rész
        if (str.includes('.')) {
            var parts = str.split('.');
            var decimals = parts[1];
            
            // Ha csak nullák vannak a tizedes résznél, elhagyjuk
            if (decimals === '0' || decimals === '00') {
                return parts[0];
            } else {
                // Vesszőre cseréljük a pontot
                return parts[0] + ',' + decimals;
            }
        }
        
        return str;
    }
    
    function updateInfoBar() {
        var table = $('#rim_returnlist').DataTable();

        var sumErtek = 0, sumQtyCalc = 0, sumQtyDealer = 0, sumQtyAwd = 0;

        table.rows().every(function() {
            /*var rowData = this.data();
            sumErtek += parseFloat(rowData.ertek) || 0;
            sumQtyCalc += parseInt(rowData.qty_calc) || 0;
            sumQtyDealer += parseInt(rowData.qty_dealer) || 0;
            sumQtyAwd += parseInt(rowData.qty_awd) || 0;*/
            var rowData = this.data();

            function normalizeNumber(value) {
                if (typeof value === 'string') {
                    value = value.replace(/,/g, ''); // ezres elválasztók eltávolítása
                }
                return parseFloat(value) || 0;
            }
        
            sumErtek += normalizeNumber(rowData.ertek);
            sumQtyCalc += normalizeNumber(rowData.qty_calc);
            sumQtyDealer += normalizeNumber(rowData.qty_dealer);
            sumQtyAwd += normalizeNumber(rowData.qty_awd);
        });

        // Infó sáv frissítése
        $("#sumErtek").text(sumErtek.toLocaleString("hu-HU"));
        $("#sumQtyCalc").text(sumQtyCalc.toLocaleString("hu-HU"));
        $("#sumQtyDealer").text(sumQtyDealer.toLocaleString("hu-HU"));
        $("#sumQtyAwd").text(sumQtyAwd.toLocaleString("hu-HU"));
    }

    function exportTableToExcel() {
        var table = $('#rim_returnlist').DataTable();
        var data = [];
        
        // Fejléc hozzáadása
        var headers = [];
        $('#rim_returnlist thead th').each(function() {
            headers.push($(this).text().trim());
        });
        data.push(headers);

        // Sorok bejárása
        table.rows().every(function() {
            var rowData = this.data();
            var row = [];
            row.push(rowData.cikkszam);
            row.push(rowData.megnevezes);
            row.push(formatToHungarian(rowData.ertek));
            row.push(rowData.qty_calc);
            row.push(rowData.qty_dealer);
            row.push(rowData.qty_awd);
            row.push((rowData.cre_all || '').split(' ')[0]);
            row.push(rowData.tipus);
            row.push(rowData.statusz);
            data.push(row);
        });

        // Excel munkalap létrehozása
        var ws = XLSX.utils.aoa_to_sheet(data);
        
        // Automatikus oszlopszélesség
        var wscols = [
            { wch: 15 }, // cikkszam
            { wch: 30 }, // megnevezes
            { wch: 15 }, // ertek
            { wch: 10 }, // qty_calc
            { wch: 10 }, // qty_dealer
            { wch: 10 }  // qty_awd
        ];
        ws['!cols'] = wscols;

        // Workbook létrehozása
        var wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "Return List");

        // Fájl letöltése
        XLSX.writeFile(wb, "ReturnList.xlsx");
    }

    function exportSupportToExcel() {
        var apiUrl = "json.html?feladat=rim_supportdb&partner_kod=`param_partnerkod`&param_year=`param_year`";

        var $button = $("#exportSupportDB"); 

        var originalText = $button.html();
        $button.prop("disabled", true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Lekérdezés...');
    
        $.getJSON(apiUrl, function(response) {
            if (!response || !response.data || response.data.length === 0) {
                alert("Nincs elérhető adat az exporthoz.");
                return;
            }
    
            var data = [];
    
            // Egyedi oszlopnevek (fejléc) létrehozása a Support adatbázishoz
            var headers = {
                "parkod": "Partner",
                "cikkszam": "Parts",
                "cikknev": "Name",
                "year": "Year",
                "pys": "PYPrevYearProtStockS",
                "stock": "YearCloseStock",
                "dat_le": "Last Exclusion Date",
                "pyc": "PrevYearConsidered",
                "dat_szla": "FirstRIMInvDate",
                "hiv_szla": "FirstRIMInvNumber",
                "dat_ps": "ProtectionStartDate",
                "buy_ly": "RIMPurchaseTotal",
                "sold_ly": "SoldTotal",
                "buy_pa": "RIMPastPSDPurch",
                "sold_ps": "PastPSDSold",
                "ps_buy_vs_sold": "PSDPurchVsSold",
                "protected_stock": "ProtectedStock"
            };
    
            // Az első sor a fejléc
            data.push(Object.values(headers));
    
            // Adatok átalakítása SheetJS-hez
            response.data.forEach(row => {
                var rowData = [];
                Object.keys(headers).forEach(header => {
                    rowData.push(row[header] || ""); // Üres string, ha nincs adat
                });
                data.push(rowData);
            });
    
            // Excel munkalap létrehozása
            var ws = XLSX.utils.aoa_to_sheet(data);
    
            // Oszlopszélesség beállítása
            var wscols = Object.keys(headers).map(() => ({ wch: 20 }));
            ws['!cols'] = wscols;
    
            // Workbook létrehozása és letöltése
            var wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Protected stock calc");
            XLSX.writeFile(wb, "Protected_stock_calc.xlsx");
        }).fail(function() {
            alert("Hiba történt az adatok lekérése közben.");
        }).always(function() {
            $button.prop("disabled", false).html(originalText);
        });
    }
    
    // Teljes állomány exportálása
    function exportFullToExcel(all = false) {
        var apiUrl = "json.html?feladat=rim_fullreturnlist&partner_kod=`param_partnerkod`&param_year=`param_year`";
        var $button = $("#exportFullDB"); 
        if (all) {
            apiUrl = "json.html?feladat=rim_fullreturnlist&param_year=`param_year`";
            $button = $("#exportFullDBAllDealers");
        }
    

        var originalText = $button.html();
        $button.prop("disabled", true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Query in progress...');
    
        $.getJSON(apiUrl, function(response) {
            if (!response || !response.data || response.data.length === 0) {
                alert("No data available for export.");
                return;
            }
    
            var data = [];
    
            // Egyedi oszlopnevek (fejléc) létrehozása a Teljes állományhoz
            var headers = {
                "year": "Year",
                "parkod": "Partner",
                "cikkszam": "Parts",
                "cikknev": "Name",
                "create": "ProcessDate",
                "qty_pyps": "PrevYearProtStock",
                "qty_sold": "SoldInYear",
                "qty_actual": "ActualStock",
                "qty_remaining": "RemainingQuantity",
                "egysegar": "UnitPrice",
                "devizanem": "Currency",
                "ertek": "LineValue",
                "limit_feletti": "AboveValueLimit",
                "pkg_size": "PackageSize",
                "pkg_full": "FullPackage",
                "qty_buyinyear": "PurchaseInYear",
                "no_purchase": "NoPurchase",
                "qty_calc": "ReturnQuantityBeforeSelloutSalesCheck",
                "qty_awdsold" : "Sell-outQuantity",
                "qty_awdsold_diff" : "DailyFileSalesVsSell-out",
                "sell_out_sales" : "Sell-outSales?",
                "qty_return" : "ReturnQuantity"
            };
    
            // Az első sor a fejléc
            data.push(Object.values(headers));
    
            // Adatok átalakítása SheetJS-hez
            response.data.forEach(row => {
                var rowData = [];
                Object.keys(headers).forEach(header => {
                    rowData.push(row[header] || ""); // Üres string, ha nincs adat
                });
                data.push(rowData);
            });
    
            // Excel munkalap létrehozása
            var ws = XLSX.utils.aoa_to_sheet(data);
    
            // Oszlopszélesség beállítása
            var wscols = Object.keys(headers).map(() => ({ wch: 20 }));
            ws['!cols'] = wscols;
    
            // Workbook létrehozása és letöltése
            var wb = XLSX.utils.book_new();
            if (all) {
                XLSX.utils.book_append_sheet(wb, ws, "Return calc all dealers");
                XLSX.writeFile(wb, "Return_calc_all_dealers.xlsx");
            } else {
                XLSX.utils.book_append_sheet(wb, ws, "Return calc");
                XLSX.writeFile(wb, "Return_calc.xlsx");
            }
        }).fail(function() {
            alert("Hiba történt az adatok lekérése közben.");
        }).always(function() {
            $button.prop("disabled", false).html(originalText);
        });
    }

    // Return list all dealers exportálása
    function exportReturnListAllDealers() {
        var apiUrl = "json.html?feladat=rim_returnlist_all_dealers&param_year=`param_year`";
        var $button = $("#btnExcelExportAllDealers");

        var originalText = $button.html();
        $button.prop("disabled", true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Query in progress...');

        $.getJSON(apiUrl, function(response) {
            if (!response || !response.data || response.data.length === 0) {
                alert("No data available for export.");
                return;
            }

            var data = [];

            // Fejléc hozzáadása
            var headers = [];
            $('#rim_returnlist thead th').each(function() {
                headers.push($(this).text().trim());
            });
            data.push(headers);

            // Sorok bejárása
            response.data.forEach(row => {
                var rowData = [];
                rowData.push(row.cikkszam);
                rowData.push(row.megnevezes);
                rowData.push(formatToHungarian(row.ertek));
                rowData.push(row.qty_calc);
                rowData.push(row.qty_dealer);
                rowData.push(row.qty_awd);
                rowData.push((row.cre_all || '').split(' ')[0]);
                rowData.push(row.tipus);
                rowData.push(row.statusz);
                data.push(rowData);
            });

            // Excel munkalap létrehozása
            var ws = XLSX.utils.aoa_to_sheet(data);

            // Automatikus oszlopszélesség
            var wscols = [
                { wch: 15 }, // cikkszam
                { wch: 30 }, // megnevezes
                { wch: 15 }, // ertek
                { wch: 10 }, // qty_calc
                { wch: 10 }, // qty_dealer
                { wch: 10 }  // qty_awd
            ];
            ws['!cols'] = wscols;

            // Workbook létrehozása
            var wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Return List All Dealers");

            // Fájl letöltése
            XLSX.writeFile(wb, "ReturnListAllDealers.xlsx");
        }).fail(function() {
            alert("Hiba történt az adatok lekérése közben.");
        }).always(function() {
            $button.prop("disabled", false).html(originalText);
        });
    }

      $(document).ready(function() {
        var rimAdmin = `felt_disp(rim_admin,"true","false")`;
        var table = $('#`program_base_name`').DataTable( {
            "drawCallback": function() {
                updateInfoBar();
            },
            "data": [],    
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.19/i18n/`felt_disp(nyelv = 'hu','Hungarian','English')`.json"
            },
            //stateSave: true,
            "processing": true,
            "columnDefs": [
                {
                    "targets": [ 0 ],
                    "data": "id",
                    "visible": false,
                    "searchable": false
                },
                {
                    "targets": [ 1 ],
                    "data": "cikkszam"
                },
                {
                    "targets": [ 2 ],
                    "data": "megnevezes"
                },
                {
                    "targets": [ 3 ],
                    "data": "ertek",
                    "render": function(data, type, row) {
                       return '<div style="text-align: right;">' + data + '</div>';
                    }
                },
                {
                    "targets": [ 4 ],
                    "data": "qty_calc",
                    "render": function(data, type, row) {
                       return '<div style="text-align: right;">' + data + '</div>';
                    }
                },
                {
                    "targets": [ 5 ],
                    "data": "qty_dealer",
                    "render": function(data, type, row) {
                        if (!rimAdmin) {
                            return '<div class="editable-cell" style="text-align: right;">' +
                                   '<input type="number" min="0" max="' + data + '" class="edit-qty" data-id="' + row.id + '" value="' + data + '">' +
                                   '<button class="save-btn" data-id="' + row.id + '">' +
                                   '<i class="">&#128190;</i>' +
                                   '</button>' +
                                   '</div>';
                        } else {
                            return '<div style="text-align: right;">' + data + '</div>'; // Csak szövegként jelenik meg, ha nem szerkeszthető
                        }
                    }
                },
                {
                    "targets": [ 6 ],
                    "data": "qty_awd",
                    "render": function(data, type, row) {
                        if (rimAdmin) {
                            return '<div class="editable-cell" style="text-align: right;">' +
                                   '<input type="number" min="0" max="9999" class="edit-qty" data-id="' + row.id + '" value="' + data + '">' +
                                   '<button class="save-btn" data-id="' + row.id + '">' +
                                   '<i class="">&#128190;</i>' +
                                   '</button>' +
                                   '</div>';
                        } else {
                            return '<div style="text-align: right;">' + data + '</div>'; // Csak szövegként jelenik meg, ha nem szerkeszthető
                        }
                    }
                },
                {
                    "targets": [ 7 ],
                    "data": "cre_all"
                },
                {
                    "targets": [ 8 ],
                    "data": "tipus"
                },
                {
                    "targets": [ 9 ],
                    "data": "statusz"
                },
            ],
            "order": [[ 1, "desc" ]],
            "search": {
                "search": "`param_kereses`"
            },
        });
        
        //$('#loadingModal').modal('show');

        $('#loadingModal').on('shown.bs.modal', function () {
            $.getJSON('json.html?feladat=rim_returnlist&partner_kod=`param_partnerkod`', function(response) {
                //console.log("Kapott JSON válasz:", response);
                table.clear().rows.add(response.data).draw(); 
                $('#loadingModal').modal('hide');
                //if ($('#loadingModal').is(':visible')) {
                //	setTimeout(() => $('#loadingModal').modal('hide'), 300);
                //}
                //$('#loadingModal').modal('hide');
            }).fail(function(xhr, status, error) {
                console.error("AJAX hiba:", error);
                $('#loadingModal .modal-body').html('<p class="text-danger">Hiba történt az adatok betöltésekor!</p>');
                setTimeout(() => $('#loadingModal').modal('hide'), 2000); // 2 mp után eltűnik
            });
        });
        $('#loadingModal').modal('show');

        $('#`program_base_name`').on('search.dt', function() {
            form_0.param_kereses.value = $('.dataTables_filter input').val();
        });

        $('#partner_adm').on('change', function() {
            loading_on();
            form_0.feladat.value = "";
            form_submit();
        });
        $('#param_year').on('change', function() {
            loading_on();
            form_0.feladat.value = "";
            form_submit();
        });

        $(document).on('click', '.save-btn', function() {
            var id = $(this).data('id');
            var input = $(this).siblings('.edit-qty');
            var qty = input.val();
    
            // Ha admin, akkor másik .w fájlra küldjük az adatokat
            var saveUrl = rimAdmin ? "json.html?feladat=rim_returnawupd&partner_kod=`param_partnerkod`" : "json.html?feladat=rim_returndealerupd&partner_kod=`param_partnerkod`";
    
            $.post(saveUrl, { id: id, qty: qty }, function(response) {
                try {
                    var jsonResponse = (typeof response === "string") ? JSON.parse(response) : response;

                    if (jsonResponse.data && jsonResponse.data.length > 0) {
                        var updatedRow = jsonResponse.data[0]; // Frissített sor
        
                        // Megkeressük a táblázatban az adott sort ID alapján
                        var rowIndex = table.row(function(idx, data, node) {
                            return data.id === updatedRow.id;
                        });
        
                        if (rowIndex !== null) {
                            // Az adott sort frissítjük az új adatokkal
                            table.row(rowIndex).data({
                                ...table.row(rowIndex).data(), // Megőrzi a többi adatot
                                ertek: updatedRow.ertek,
                                qty_awd: updatedRow.qty_awd,
                                qty_dealer: updatedRow.qty_dealer,
                                cre_dat: updatedRow.cre_dat,
                                cre_tim: updatedRow.cre_tim,
                                cre_all: updatedRow.cre_all,
                                tipus: updatedRow.tipus
                            }).draw(false);
                        }
                        $.growl.notice({ message: "`fordit(szotar_kategoria,"adatmodositas_sikeres",nyelv,"Adatmódosítás sikeres",szotar_kontextus)`!" });
                    }
                    if (jsonResponse.error && jsonResponse.error.length > 0) {
                        $.growl.error({ message: jsonResponse.error[0].text });
                    }
                    if (jsonResponse.warning && jsonResponse.warning.length > 0) {
                        $.growl.warning({ message: jsonResponse.warning[0].text });
                    }
                } catch (error) {
                    $.growl.error({ message: "`fordit(szotar_kategoria,"adatmodositas_sikertelen",nyelv,"Adatmódosítás sikertelen",szotar_kontextus)`!" });
                    console.error("Hiba a JSON feldolgozásakor:", error, response);
                }
            });
        });

        if (rimAdmin) {
            $("#admin-buttons").append('<button id="add-row" class="btn btn-primary"><i class="fas fa-plus"></i> Új Sor</button>');

            // Ha rákattintunk az "Új Sor" gombra, megnyílik a modal ablak
            $(document).on('click', '#add-row', function() {
                $('#addRowModal').modal('show');
            });

            // Ha rákattintunk a "Hozzáadás" gombra a modalban
            $('#saveRow').click(function() {
                var cikkszam = $('#cikkszam').val().trim();
                var mennyiseg = $('#mennyiseg').val().trim();

                if (cikkszam && mennyiseg) {
                    var addUrl = "json.html?feladat=rim_returnnew&partner_kod=`param_partnerkod`"; 

                    $.post(addUrl, { cikkszam: cikkszam, qty: mennyiseg }, function(response) {
                        try {
                            var jsonResponse = (typeof response === "string") ? JSON.parse(response) : response; // JSON ellenőrzés

                            if (jsonResponse.data && jsonResponse.data.length > 0) {
                                var newRow = jsonResponse.data[0];

                                // Új sor hozzáadása a táblázathoz
                                table.row.add({
                                    id: newRow.id,
                                    cikkszam: newRow.cikkszam,
                                    megnevezes: newRow.megnevezes,
                                    ertek: newRow.ertek,
                                    qty_calc: newRow.qty_calc,
                                    qty_dealer: newRow.qty_dealer,
                                    qty_awd: newRow.qty_awd,
                                    cre_all: newRow.cre_all,
                                    tipus: newRow.tipus,
                                    statusz: newRow.statusz
                                }).draw(false);

                                // Modal bezárása és űrlap törlése
                                $('#addRowModal').modal('hide');
                                $('#cikkszam').removeAttr('required').val('');
                                $('#mennyiseg').removeAttr('required').val('');

                                $.growl.notice({ message: "`fordit(szotar_kategoria,"letrehozas_sikeres",nyelv,"Létrehozás sikeres",szotar_kontextus)`!" });
                            }
                            if (jsonResponse.error && jsonResponse.error.length > 0) {
                                $.growl.error({ message: jsonResponse.error[0].text });
                            }
                            if (jsonResponse.warning && jsonResponse.warning.length > 0) {
                                $.growl.warning({ message: jsonResponse.warning[0].text });
                            }
                        } catch (error) {
                            console.error("Hiba az új sor feldolgozásakor:", error, response);
                        }
                    });
                }
            });

            // Az "X" és a "Mégse" gombra kattintva a modal biztosan bezáródik
            $(document).on('click', '.close-modal', function() {
                $('#addRowModal').modal('hide');
            });

            $('#addRowModal').on('shown.bs.modal', function () {
                $('#cikkszam').attr('required', true);
                $('#mennyiseg').attr('required', true);
                $('#cikkszam').focus();
            });
            
            $('#addRowModal').on('hidden.bs.modal', function() {
                $('#cikkszam').removeAttr('required').val('');
                $('#mennyiseg').removeAttr('required').val('');
            });
        } else {
            // Ha rákattintunk a "Véglegesít" gombra
            $('#btnapprove').click(function() {
                let $button = $(this);
                Swal.fire({
                    title: "`fordit(szotar_kategoria,"biztosan_veglegesited",nyelv,"Biztosan véglegesíted",szotar_kontextus)`?",
                    text: "`fordit(szotar_kategoria,"ezt_a_muveletet_nem_lehet_visszavonni",nyelv,"Ezt a műveletet nem lehet visszavonni",szotar_kontextus)`!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#28a745",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "`fordit(szotar_kategoria,"igen_veglegesitem",nyelv,"Igen, véglegesítem",szotar_kontextus)`!",
                    cancelButtonText: "`fordit(szotar_kategoria,"megsem",nyelv,"Mégsem",szotar_kontextus)`"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $button.prop("disabled", true);
                        let originalHtml = $button.html(); // Eredeti gomb szöveg mentése
                        $button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Feldolgozás...');

                        var addUrl = "json.html?feladat=rim_returnapprove&partner_kod=`param_partnerkod`"; 

                        $.post(addUrl, { }, function(response) {
                            try {
                                var jsonResponse = (typeof response === "string") ? JSON.parse(response) : response;
                    
                                if (jsonResponse.error && jsonResponse.error.length > 0) {
                                    $.growl.error({ message: jsonResponse.error[0].text });
                                } else if (jsonResponse.warning && jsonResponse.warning.length > 0) {
                                    $.growl.warning({ message: jsonResponse.warning[0].text });
                                } else {
                                    $.growl.notice({ message: "`fordit(szotar_kategoria,'veglegesites_sikeres',nyelv,'Véglegesítés sikeres',szotar_kontextus)`!" });  
                                    $button.html(originalHtml);
                                }
                            } catch (error) {
                                console.error("Hiba a feldolgozásakor:", error, response);
                                $button.prop("disabled", false).html(originalHtml);
                            }
                        });
                    }
                });
            });
        }

        $(document).on("click", "#btnExcelExport", function() {
            exportTableToExcel();
        });

        // Support adatbázis export gomb
        $(document).on("click", "#exportSupportDB", function(e) {
            e.preventDefault();
            exportSupportToExcel();
        });

        // Teljes állomány export gomb
        $(document).on("click", "#exportFullDB", function(e) {
            e.preventDefault();
            exportFullToExcel();

            });
        // Teljes állomány export gomb összes partnerre
        $(document).on("click", "#exportFullDBAllDealers", function(e) {
            e.preventDefault();
            exportFullToExcel(true);
        });

        // Return list all dealers export gomb
        $(document).on("click", "#btnExcelExportAllDealers", function(e) {
            e.preventDefault();
            exportReturnListAllDealers();
        });

    });
  </script>
  <input type="hidden" name="kod" value="">
    <div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content text-center">
                <div class="modal-body">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">`fordit(szotar_kategoria,"betoltes",nyelv,"Betöltés",szotar_kontextus)`...</span>
                    </div>
                    <p class="mt-3 font-weight-bold">`fordit(szotar_kategoria,"adatok_betoltese",nyelv,"Adatok betöltése",szotar_kontextus)`...</p>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="addRowModal" tabindex="-1" aria-labelledby="addRowModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRowModalLabel">`fordit(szotar_kategoria,"uj_sor_hozzaadasa",nyelv,"Új sor hozzáadása",szotar_kontextus)`</h5>
                    <button type="button" class="close close-modal" data-dismiss="modal" aria-label="`fordit(szotar_kategoria,"bezaras",nyelv,"Bezárás",szotar_kontextus)`">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addRowForm">
                        <div class="form-group">
                            <label for="cikkszam">`fordit(szotar_kategoria,"cikkszam",nyelv,"Cikkszám",szotar_kontextus)`:</label>
                            <input type="text" class="form-control" name="cikkszam" id="cikkszam">
                        </div>
                        <div class="form-group">
                            <label for="mennyiseg">`fordit(szotar_kategoria,"mennyiseg",nyelv,"Mennyiség",szotar_kontextus)`:</label>
                            <input type="number" class="form-control" name="mennyiseg" id="mennyiseg" min="1">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal" data-dismiss="modal">`fordit(szotar_kategoria,"megsem",nyelv,"Mégsem",szotar_kontextus)`</button>
                    <button type="button" class="btn btn-primary" id="saveRow">`fordit(szotar_kategoria,"hozzaadas",nyelv,"Hozzáadás",szotar_kontextus)`</button>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
      <div class="col-2">
        <select name="param_year" id="param_year" class="form-select mb-3" aria-label="`fordit(szotar_kategoria,"ev",nyelv,"Év",szotar_kontextus)`">
        <script language="SpeedScript">
          for each ttyears no-lock by ttyears.yea-r:
        </script>
        <option value="`ttyears.yea-r`" `felt_disp(ttyears.yea-r = param_year,"selected","")`>`ttyears.yea-r`</option>
        <script language="SpeedScript">
            end.
        </script>
      </select>
      </div>
      <script language="SpeedScript">
      if rim_admin then do:
      </script>
        <div class="col">
          <select name="partner_adm" id="partner_adm" class="form-select mb-3" aria-label="`fordit(szotar_kategoria,"osszes_partner",nyelv,"Összes partner",szotar_kontextus)`">
          <option>`fordit(szotar_kategoria,"kerem_valasszon_partnert",nyelv,"Kérem válasszon partnert",szotar_kontextus)`</option>
          <script language="SpeedScript">
              for each spc_partn where spc_partn.aktiv and spc_partn.tmk-kod <> "" no-lock by spc_partn.szl-nev:
                  find first tmk.cegukart where tmk.cegukart.kod = spc_partn.tmk-kod no-lock no-error.
                  if not avail tmk.cegukart then next.
                  if tmk.cegukart.rim-start = "" then next.
                  find first tmk.partner where tmk.partner.kod = spc_partn.tmk-kod no-lock no-error.
                  if not avail tmk.partner then next.
          </script>
          <option value="`spc_partn.tmk-kod`" `felt_disp(spc_partn.tmk-kod = partner_adm,"selected","")`>`spc_partn.szl-nev` - `tmk.cegukart.kod-old`</option>
          <script language="SpeedScript">
              end.
          </script>
          </select>
        </div>
      <script language="SpeedScript">
    end.
    </script>
        <div class="col-2 text-right">
            <button id="btnExcelExport" class="btn btn-success mb-3">`fordit(szotar_kategoria,"return_list",nyelv,"Return list",szotar_kontextus)`</button>
        </div>
    </div>
    <div id="dataInfoBar" class="alert alert-info text-center" style="font-size: 16px; font-weight: bold;">
        `fordit(szotar_kategoria,"osszesitett_ertek",nyelv,"Összesített Érték",szotar_kontextus)`: <span id="sumErtek">0</span> | 
        `fordit(szotar_kategoria,"szamitott_darab",nyelv,"Számított Darab",szotar_kontextus)`: <span id="sumQtyCalc">0</span> |
        `fordit(szotar_kategoria,"dealer_darab",nyelv,"Dealer Darab",szotar_kontextus)`: <span id="sumQtyDealer">0</span> |
        `fordit(szotar_kategoria,"aw_darab",nyelv,"AW Darab",szotar_kontextus)`: <span id="sumQtyAwd">0</span>
    </div>
    <div class="row">
        <div class="col">
            <table id="`program_base_name`" class="table table-hover">
                <thead>
                <tr>
                    <th></th>   
                    <th scope="col">`fordit(szotar_kategoria,"cikkszam",nyelv,"Cikkszám",szotar_kontextus)`</th>
                    <th scope="col">`fordit(szotar_kategoria,"megnevezes",nyelv,"Megnevezés",szotar_kontextus)`</th>
                    <th scope="col" style="text-align: right">`fordit(szotar_kategoria,"ertek",nyelv,"Érték",szotar_kontextus)`</th>
                    <th scope="col" style="text-align: right">`fordit(szotar_kategoria,"szamitott_darab",nyelv,"Számított darab",szotar_kontextus)`</th>
                    <th scope="col" style="text-align: right">`fordit(szotar_kategoria,"dealer_darab",nyelv,"Dealer darab",szotar_kontextus)`</th>
                    <th scope="col" style="text-align: right">`fordit(szotar_kategoria,"aw_darab",nyelv,"AW darab",szotar_kontextus)`</th>
                    <th scope="col">`fordit(szotar_kategoria,"létrehzás_datuma",nyelv,"Léterhozás dátuma",szotar_kontextus)`</th>
                    <th scope="col">`fordit(szotar_kategoria,"tipus",nyelv,"Típus",szotar_kontextus)`</th>
                    <th scope="col">`fordit(szotar_kategoria,"status",nyelv,"Status",szotar_kontextus)`</th>
                </tr>
                </thead>
            </table>
        </div>
    </div>
    <div class="row mt-3">
    <script language="SpeedScript">
    if not rim_admin then do:
    </script>
        <div class="col">
            <div id="dealer-buttons">
                <button class="btn btn-success" role="button" id="btnapprove"><i class="iconify" data-icon="mdi-check-circle"></i> `fordit(szotar_kategoria,"veglegesit",nyelv,"Véglegesít",szotar_kontextus)`</button>
            </div>
        </div>
    <script language="SpeedScript">
    end.
    </script>
        <div class="col">
            <div id="admin-buttons"></div>
        </div>
        <script language="SpeedScript">
        if rim_admin then do:
        </script>
        <div class="col text-right">
            <a href="#" id="exportSupportDB" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> Protected stock calc
            </a>
            <a href="#" id="exportFullDB" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> Return calc
            </a>
            <a href="#" id="exportFullDBAllDealers" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> Return calc all dealers
            </a>
            <a href="#" id="btnExcelExportAllDealers" class="btn btn-outline-success">
                <i class="fas fa-file-excel"></i> `fordit(szotar_kategoria,"return_list_all_dealers",nyelv,"Return list all dealers",szotar_kontextus)`
            </a>
        </div>
        <script language="SpeedScript">
        end.
        </script>
    </div>
    <script language="SpeedScript">
  end procedure.

  procedure megnez.
  end procedure.

  procedure tartalom.
</script>
<form id="form_0" method="post" onsubmit="return false">
  <script language="speedscript">
    run .\default_formvariables.html.
    run .\js_datepicker_lang.html.
    if bejelentkezve then do:
        run .\vcard_start.html (input "", input "", input "").
        case feladat:
            when "megnez" then do:
                run megnez.
            end.
            otherwise do:
                run lista.
            end.
        end case.
        run .\vcard_stop.html.
    end.
  </script>
</form>
<script language="speedscript">
  end procedure.

  procedure init.
    for each rimreturnlist where (rimreturnlist.par-kod = param_partnerkod or param_partnerkod = "") and rimreturnlist.yea-r <> "" no-lock group by rimreturnlist.yea-r:
        if first-of(rimreturnlist.yea-r) then do:
            create ttyears.
            ttyears.yea-r = rimreturnlist.yea-r.
            if param_year = "" then param_year = rimreturnlist.yea-r.
        end.
    end.
  end procedure.

  bejelentkezve = kod_ok(kodolt_login) and login_hozzafer(user_login,program_jog).
  app_nev = jog_neve(program_jog,nyelv).
  function_path = function_path(program_jog,nyelv).
  rim_admin = login_hozzafer(user_login,"TERULETI").

  gv_formatum_decimal = "->,>>>,>>>,>>9.99".

  uj_layout ("css_base/05","/" + param_web_dir + "/growl/stylesheets/jquery.growl.css").
  uj_layout ("css_base/06","/" + param_web_dir + "/datatables/datatables.min.css").
  uj_layout ("css_base/07","/" + param_web_dir + "/datatables/dataTables.bootstrap4.css").
  uj_layout ("css_base/08","/" + param_web_dir + "/jquery-ui-1.12.1/jquery-ui.min.css").
  uj_layout ("css_base/09","/" + param_web_dir + "/fa/all.min.css").
  uj_layout ("css_base/99","/" + param_web_dir + "/css/final.css").
  uj_layout ("js_base/03","/" + param_web_dir + "/growl/javascripts/jquery.growl.js").
  uj_layout ("js_base/04","/" + param_web_dir + "/datatables/datatables.min.js").
  uj_layout ("js_base/05","/" + param_web_dir + "/jquery-ui-1.12.1/jquery-ui.min.js").
  uj_layout ("js_base/06","/" + param_web_dir + "/jquery/jquery.maskedinput.min.js").
  uj_layout ("js_base/07","/" + param_web_dir + "/sweetalert/<EMAIL>").
  uj_layout ("js_base/08","/" + param_web_dir + "/xlsx/xlsx.full.min.js").

  run .\run_layout.html ("start").
  
  run init.
  run tartalom.

  run .\run_layout.html ("stop").
</script>
